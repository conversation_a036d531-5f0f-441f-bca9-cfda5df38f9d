// 修复CSS变量的脚本
const fs = require('fs');
const path = require('path');

// CSS变量映射
const cssVars = {
  'var(--primary-color)': '#FFD700',
  'var(--secondary-color)': '#1E3A8A',
  'var(--background-color)': '#FEFEFE',
  'var(--text-color)': '#2D3748',
  'var(--success-color)': '#10B981',
  'var(--warning-color)': '#F59E0B',
  'var(--error-color)': '#EF4444',
  'var(--font-size-large)': '18px',
  'var(--font-size-medium)': '16px',
  'var(--font-size-small)': '14px',
  'var(--font-size-mini)': '12px',
  'var(--spacing-large)': '20px',
  'var(--spacing-medium)': '16px',
  'var(--spacing-small)': '12px',
  'var(--spacing-mini)': '8px',
  'var(--border-radius)': '8px',
  'var(--button-height)': '44px'
};

// 需要处理的文件
const files = [
  '../pages/index/index.wxss',
  '../pages/game/game.wxss',
  '../pages/test/test.wxss'
];

function replaceCSSVars(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 替换所有CSS变量
    for (const [cssVar, value] of Object.entries(cssVars)) {
      if (content.includes(cssVar)) {
        content = content.replace(new RegExp(cssVar.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
        changed = true;
      }
    }
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`⏭️  无需修复: ${filePath}`);
    }
  } catch (error) {
    console.log(`❌ 处理失败: ${filePath} - ${error.message}`);
  }
}

// 处理所有文件
files.forEach(file => {
  const fullPath = path.resolve(__dirname, file);
  if (fs.existsSync(fullPath)) {
    replaceCSSVars(fullPath);
  } else {
    console.log(`⚠️  文件不存在: ${fullPath}`);
  }
});

console.log('🎉 CSS变量修复完成！');
