/* app.wxss - 全局样式 */

/* 全局变量定义 */
page {
  --primary-color: #FFD700;
  --secondary-color: #1E3A8A;
  --background-color: #FEFEFE;
  --text-color: #2D3748;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
  
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-small: 14px;
  --font-size-mini: 12px;
  
  --spacing-large: 20px;
  --spacing-medium: 16px;
  --spacing-small: 12px;
  --spacing-mini: 8px;
  
  --border-radius: 8px;
  --button-height: 44px;
}

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
}

/* 通用布局类 */
.container {
  padding: var(--spacing-large);
  min-height: 100vh;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 通用按钮样式 */
.btn {
  height: var(--button-height);
  border-radius: var(--border-radius);
  border: none;
  font-size: var(--font-size-medium);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), #FFA500);
  color: #333;
}

.btn-primary:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

/* 通用文本样式 */
.text-large {
  font-size: var(--font-size-large);
  font-weight: bold;
}

.text-medium {
  font-size: var(--font-size-medium);
}

.text-small {
  font-size: var(--font-size-small);
}

.text-mini {
  font-size: var(--font-size-mini);
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

/* 通用间距类 */
.mt-large { margin-top: var(--spacing-large); }
.mt-medium { margin-top: var(--spacing-medium); }
.mt-small { margin-top: var(--spacing-small); }
.mt-mini { margin-top: var(--spacing-mini); }

.mb-large { margin-bottom: var(--spacing-large); }
.mb-medium { margin-bottom: var(--spacing-medium); }
.mb-small { margin-bottom: var(--spacing-small); }
.mb-mini { margin-bottom: var(--spacing-mini); }

.p-large { padding: var(--spacing-large); }
.p-medium { padding: var(--spacing-medium); }
.p-small { padding: var(--spacing-small); }
.p-mini { padding: var(--spacing-mini); }

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(-5px); }
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-medium);
  margin-bottom: var(--spacing-medium);
}
