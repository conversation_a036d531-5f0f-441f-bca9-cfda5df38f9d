/* app.wxss - 全局样式 */

/* 全局样式定义 */

/* 重置样式 */
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}

page {
  background-color: #FEFEFE;
  color: #2D3748;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
}

/* 通用布局类 */
.container {
  padding: 20px;
  min-height: 100vh;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 通用按钮样式 */
.btn {
  height: 44px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
}

.btn-primary:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.btn-secondary {
  background-color: #1E3A8A;
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #FFD700;
  color: #FFD700;
}

/* 通用文本样式 */
.text-large {
  font-size: 18px;
  font-weight: bold;
}

.text-medium {
  font-size: 16px;
}

.text-small {
  font-size: 14px;
}

.text-mini {
  font-size: 12px;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #FFD700;
}

.text-secondary {
  color: #1E3A8A;
}

.text-success {
  color: #10B981;
}

.text-warning {
  color: #F59E0B;
}

.text-error {
  color: #EF4444;
}

/* 通用间距类 */
.mt-large { margin-top: 20px; }
.mt-medium { margin-top: 16px; }
.mt-small { margin-top: 12px; }
.mt-mini { margin-top: 8px; }

.mb-large { margin-bottom: 20px; }
.mb-medium { margin-bottom: 16px; }
.mb-small { margin-bottom: 12px; }
.mb-mini { margin-bottom: 8px; }

.p-large { padding: 20px; }
.p-medium { padding: 16px; }
.p-small { padding: 12px; }
.p-mini { padding: 8px; }

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(-5px); }
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
}
