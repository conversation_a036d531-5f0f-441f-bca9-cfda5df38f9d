// app.js
App({
  globalData: {
    userInfo: null,
    gameData: {
      currentLevel: 1,
      totalScore: 0,
      unlockedLevels: 1,
      achievements: []
    },
    settings: {
      soundEnabled: true,
      vibrationEnabled: true,
      difficulty: 'normal'
    }
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('成语迷宫小程序启动');
    this.initUserData();
    this.checkForUpdates();
  },

  onShow() {
    // 小程序显示时执行
    console.log('成语迷宫小程序显示');
  },

  onHide() {
    // 小程序隐藏时执行
    this.saveUserData();
  },

  // 初始化用户数据
  initUserData() {
    try {
      const gameData = wx.getStorageSync('gameData');
      const settings = wx.getStorageSync('settings');
      
      if (gameData) {
        this.globalData.gameData = { ...this.globalData.gameData, ...gameData };
      }
      
      if (settings) {
        this.globalData.settings = { ...this.globalData.settings, ...settings };
      }
    } catch (error) {
      console.error('初始化用户数据失败:', error);
    }
  },

  // 保存用户数据
  saveUserData() {
    try {
      wx.setStorageSync('gameData', this.globalData.gameData);
      wx.setStorageSync('settings', this.globalData.settings);
    } catch (error) {
      console.error('保存用户数据失败:', error);
    }
  },

  // 检查小程序更新
  checkForUpdates() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
    }
  },

  // 更新游戏数据
  updateGameData(data) {
    this.globalData.gameData = { ...this.globalData.gameData, ...data };
    this.saveUserData();
  },

  // 更新设置
  updateSettings(settings) {
    this.globalData.settings = { ...this.globalData.settings, ...settings };
    this.saveUserData();
  }
});
