package utils

import (
	"encoding/json"
	"log"
)

// ToJSONString 将对象转换为JSON字符串
func ToJSONString(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		log.Printf("Failed to marshal to JSON: %v", err)
		return "{}"
	}
	return string(data)
}

// FromJSONString 从JSON字符串解析对象
func FromJSONString(jsonStr string, v interface{}) error {
	return json.Unmarshal([]byte(jsonStr), v)
}

// ToJSONBytes 将对象转换为JSON字节数组
func ToJSONBytes(v interface{}) []byte {
	data, err := json.Marshal(v)
	if err != nil {
		log.Printf("Failed to marshal to JSON: %v", err)
		return []byte("{}")
	}
	return data
}
