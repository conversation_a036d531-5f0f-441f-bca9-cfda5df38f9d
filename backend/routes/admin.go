package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// SetupAdminRoutes 设置管理员相关路由
func SetupAdminRoutes(router *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {
	router.GET("/stats", getSystemStats(db, rdb))
	router.POST("/cache/clear", clearCache(rdb))
	router.GET("/users", getUsers(db))
	router.GET("/levels", getAdminLevels(db))
}

// getSystemStats 获取系统统计
func getSystemStats(db *gorm.DB, rdb *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户总数
		var userCount int64
		db.Model(&struct {
			ID uint64 `gorm:"primaryKey"`
		}{}).Table("users").Count(&userCount)

		// 获取关卡总数
		var levelCount int64
		db.Model(&struct {
			ID uint64 `gorm:"primaryKey"`
		}{}).Table("levels").Count(&levelCount)

		// 获取游戏记录总数
		var recordCount int64
		db.Model(&struct {
			ID uint64 `gorm:"primaryKey"`
		}{}).Table("user_game_records").Count(&recordCount)

		// 获取成语总数
		var idiomCount int64
		db.Model(&struct {
			ID uint64 `gorm:"primaryKey"`
		}{}).Table("idioms").Count(&idiomCount)

		stats := gin.H{
			"users":        userCount,
			"levels":       levelCount,
			"game_records": recordCount,
			"idioms":       idiomCount,
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    stats,
			"message": "获取成功",
		})
	}
}

// clearCache 清除缓存
func clearCache(rdb *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := rdb.Context()
		if err := rdb.FlushDB(ctx).Err(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "清除缓存失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"message": "缓存清除成功",
		})
	}
}

// getUsers 获取用户列表（管理员）
func getUsers(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var users []struct {
			ID             uint64 `json:"id"`
			Nickname       string `json:"nickname"`
			TotalScore     int    `json:"total_score"`
			CurrentLevel   int    `json:"current_level"`
			UnlockedLevels int    `json:"unlocked_levels"`
			CreatedAt      string `json:"created_at"`
		}

		if err := db.Table("users").
			Select("id, nickname, total_score, current_level, unlocked_levels, created_at").
			Order("created_at DESC").
			Limit(100).
			Find(&users).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取用户列表失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    users,
			"message": "获取成功",
		})
	}
}

// getAdminLevels 获取关卡列表（管理员）
func getAdminLevels(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var levels []struct {
			ID          uint64 `json:"id"`
			LevelNumber int    `json:"level_number"`
			Name        string `json:"name"`
			Difficulty  int8   `json:"difficulty"`
			IsActive    bool   `json:"is_active"`
			CreatedAt   string `json:"created_at"`
		}

		if err := db.Table("levels").
			Select("id, level_number, name, difficulty, is_active, created_at").
			Order("level_number ASC").
			Find(&levels).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取关卡列表失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    levels,
			"message": "获取成功",
		})
	}
}
