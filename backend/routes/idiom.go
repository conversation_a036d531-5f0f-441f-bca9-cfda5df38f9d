package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/services"
)

// SetupIdiomRoutes 设置成语相关路由
func SetupIdiomRoutes(router *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {
	idiomService := services.NewIdiomService(db, rdb)

	router.GET("/", getIdioms(idiomService))
	router.GET("/:id", getIdiom(idiomService))
	router.GET("/search", searchIdioms(idiomService))
	router.GET("/random", getRandomIdioms(idiomService))
}

// getIdioms 获取成语列表
func getIdioms(idiomService *services.IdiomService) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.<PERSON><PERSON>ultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.<PERSON>faultQuer<PERSON>("page_size", "20"))
		difficulty := c.Query("difficulty")
		category := c.Query("category")

		idioms, total, err := idiomService.GetIdioms(page, pageSize, difficulty, category)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取成语列表失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"idioms": idioms,
				"total":  total,
				"page":   page,
				"size":   pageSize,
			},
			"message": "获取成功",
		})
	}
}

// getIdiom 获取单个成语详情
func getIdiom(idiomService *services.IdiomService) gin.HandlerFunc {
	return func(c *gin.Context) {
		idiomID, err := strconv.ParseUint(c.Param("id"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的成语ID",
				"message": err.Error(),
			})
			return
		}

		idiom, err := idiomService.GetIdiom(idiomID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "成语不存在",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    idiom,
			"message": "获取成功",
		})
	}
}

// searchIdioms 搜索成语
func searchIdioms(idiomService *services.IdiomService) gin.HandlerFunc {
	return func(c *gin.Context) {
		keyword := c.Query("keyword")
		if keyword == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "搜索关键词不能为空",
				"message": "请提供搜索关键词",
			})
			return
		}

		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

		idioms, total, err := idiomService.SearchIdioms(keyword, page, pageSize)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "搜索失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"idioms": idioms,
				"total":  total,
				"page":   page,
				"size":   pageSize,
			},
			"message": "搜索成功",
		})
	}
}

// getRandomIdioms 获取随机成语
func getRandomIdioms(idiomService *services.IdiomService) gin.HandlerFunc {
	return func(c *gin.Context) {
		count, _ := strconv.Atoi(c.DefaultQuery("count", "10"))
		difficulty := c.Query("difficulty")

		if count > 50 {
			count = 50 // 限制最大数量
		}

		idioms, err := idiomService.GetRandomIdioms(count, difficulty)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取随机成语失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    idioms,
			"message": "获取成功",
		})
	}
}
