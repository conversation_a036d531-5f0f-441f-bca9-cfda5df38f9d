package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/models"
	"idiom-maze/services"
)

// SetupUserRoutes 设置用户相关路由
func SetupUserRoutes(router *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {
	userService := services.NewUserService(db, rdb)

	router.POST("/login", login(userService))
	router.GET("/profile/:id", getUserProfile(userService))
	router.PUT("/profile", updateUserProfile(userService))
	router.GET("/stats/:id", getUserStats(userService))
}

// login 用户登录
func login(userService *services.UserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.CreateUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.J<PERSON>(http.StatusBadRequest, gin.H{
				"error":   "请求参数错误",
				"message": err.Error(),
			})
			return
		}

		user, token, err := userService.Login(&req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "登录失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"user":  user.ToResponse(),
				"token": token,
			},
			"message": "登录成功",
		})
	}
}

// getUserProfile 获取用户资料
func getUserProfile(userService *services.UserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的用户ID",
				"message": err.Error(),
			})
			return
		}

		user, err := userService.GetUserByID(userID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "用户不存在",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    user.ToResponse(),
			"message": "获取成功",
		})
	}
}

// updateUserProfile 更新用户资料
func updateUserProfile(userService *services.UserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.UpdateUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "请求参数错误",
				"message": err.Error(),
			})
			return
		}

		userID := c.GetUint64("user_id")
		if userID == 0 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "用户未登录",
				"message": "请先登录",
			})
			return
		}

		user, err := userService.UpdateUser(userID, &req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "更新失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    user.ToResponse(),
			"message": "更新成功",
		})
	}
}

// getUserStats 获取用户统计
func getUserStats(userService *services.UserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的用户ID",
				"message": err.Error(),
			})
			return
		}

		stats, err := userService.GetUserStats(userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取统计失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    stats,
			"message": "获取成功",
		})
	}
}
