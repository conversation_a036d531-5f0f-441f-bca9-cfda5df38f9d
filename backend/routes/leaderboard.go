package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/services"
)

// SetupLeaderboardRoutes 设置排行榜相关路由
func SetupLeaderboardRoutes(router *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {
	leaderboardService := services.NewLeaderboardService(db, rdb)

	router.GET("/total", getTotalScoreLeaderboard(leaderboardService))
	router.GET("/level/:levelId", getLevelLeaderboard(leaderboardService))
	router.GET("/speed", getSpeedLeaderboard(leaderboardService))
	router.GET("/user/:userId", getUserRanking(leaderboardService))
}

// getTotalScoreLeaderboard 获取总分排行榜
func getTotalScoreLeaderboard(leaderboardService *services.LeaderboardService) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))

		leaderboard, total, err := leaderboardService.GetTotalScoreLeaderboard(page, pageSize)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取排行榜失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"leaderboard": leaderboard,
				"total":       total,
				"page":        page,
				"size":        pageSize,
			},
			"message": "获取成功",
		})
	}
}

// getLevelLeaderboard 获取关卡排行榜
func getLevelLeaderboard(leaderboardService *services.LeaderboardService) gin.HandlerFunc {
	return func(c *gin.Context) {
		levelID, err := strconv.ParseUint(c.Param("levelId"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的关卡ID",
				"message": err.Error(),
			})
			return
		}

		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))

		leaderboard, total, err := leaderboardService.GetLevelLeaderboard(levelID, page, pageSize)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取关卡排行榜失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"leaderboard": leaderboard,
				"total":       total,
				"page":        page,
				"size":        pageSize,
				"level_id":    levelID,
			},
			"message": "获取成功",
		})
	}
}

// getSpeedLeaderboard 获取速度排行榜
func getSpeedLeaderboard(leaderboardService *services.LeaderboardService) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))

		leaderboard, total, err := leaderboardService.GetSpeedLeaderboard(page, pageSize)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取速度排行榜失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"leaderboard": leaderboard,
				"total":       total,
				"page":        page,
				"size":        pageSize,
			},
			"message": "获取成功",
		})
	}
}

// getUserRanking 获取用户排名
func getUserRanking(leaderboardService *services.LeaderboardService) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := strconv.ParseUint(c.Param("userId"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的用户ID",
				"message": err.Error(),
			})
			return
		}

		ranking, err := leaderboardService.GetUserRanking(userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取用户排名失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    ranking,
			"message": "获取成功",
		})
	}
}
