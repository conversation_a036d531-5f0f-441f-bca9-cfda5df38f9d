package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/models"
	"idiom-maze/services"
)

// SetupGameRoutes 设置游戏相关路由
func SetupGameRoutes(router *gin.RouterGroup, db *gorm.DB, rdb *redis.Client) {
	gameService := services.NewGameService(db, rdb)

	router.GET("/levels", getLevels(gameService))
	router.GET("/levels/:id", getLevel(gameService))
	router.POST("/start", startGame(gameService))
	router.POST("/save-progress", saveGameProgress(gameService))
	router.GET("/progress/:userId", getGameProgress(gameService))
	router.POST("/complete", completeGame(gameService))
	router.GET("/maze/generate", generateMaze(gameService))
}

// getLevels 获取关卡列表
func getLevels(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
		difficulty := c.Query("difficulty")

		levels, total, err := gameService.GetLevels(page, pageSize, difficulty)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取关卡列表失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"data": gin.H{
				"levels": levels,
				"total":  total,
				"page":   page,
				"size":   pageSize,
			},
			"message": "获取成功",
		})
	}
}

// getLevel 获取单个关卡详情
func getLevel(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		levelIDInt, err := strconv.ParseUint(c.Param("id"), 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的关卡ID",
				"message": err.Error(),
			})
			return
		}
		levelID := uint(levelIDInt)

		level, err := gameService.GetLevel(levelID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "关卡不存在",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    level,
			"message": "获取成功",
		})
	}
}

// startGame 开始游戏
func startGame(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.StartGameRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "请求参数错误",
				"message": err.Error(),
			})
			return
		}

		// 从JWT中获取用户ID（这里简化处理）
		userID64 := c.GetUint64("user_id")
		if userID64 == 0 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "用户未登录",
				"message": "请先登录",
			})
			return
		}
		userID := uint(userID64)

		gameSession, err := gameService.StartGame(userID, req.LevelID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "开始游戏失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    gameSession,
			"message": "游戏开始成功",
		})
	}
}

// saveGameProgress 保存游戏进度
func saveGameProgress(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.SaveProgressRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "请求参数错误",
				"message": err.Error(),
			})
			return
		}

		userID64 := c.GetUint64("user_id")
		if userID64 == 0 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "用户未登录",
				"message": "请先登录",
			})
			return
		}
		userID := uint(userID64)

		err := gameService.SaveProgress(userID, &req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "保存进度失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"message": "保存成功",
		})
	}
}

// getGameProgress 获取游戏进度
func getGameProgress(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID64, err := strconv.ParseUint(c.Param("userId"), 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "无效的用户ID",
				"message": err.Error(),
			})
			return
		}
		userID := uint(userID64)

		levelID64, _ := strconv.ParseUint(c.Query("level_id"), 10, 64)
		levelID := uint(levelID64)

		progress, err := gameService.GetProgress(userID, levelID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "获取进度失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    progress,
			"message": "获取成功",
		})
	}
}

// completeGame 完成游戏
func completeGame(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.CompleteGameRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "请求参数错误",
				"message": err.Error(),
			})
			return
		}

		userID64 := c.GetUint64("user_id")
		if userID64 == 0 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "用户未登录",
				"message": "请先登录",
			})
			return
		}
		userID := uint(userID64)

		result, err := gameService.CompleteGame(userID, &req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "完成游戏失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    result,
			"message": "游戏完成",
		})
	}
}

// generateMaze 生成迷宫
func generateMaze(gameService *services.GameService) gin.HandlerFunc {
	return func(c *gin.Context) {
		size, _ := strconv.Atoi(c.DefaultQuery("size", "7"))
		difficulty, _ := strconv.Atoi(c.DefaultQuery("difficulty", "1"))

		if size < 5 || size > 15 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "迷宫大小必须在5-15之间",
				"message": "无效的迷宫大小",
			})
			return
		}

		maze, err := gameService.GenerateMaze(size, difficulty)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "生成迷宫失败",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    maze,
			"message": "生成成功",
		})
	}
}
