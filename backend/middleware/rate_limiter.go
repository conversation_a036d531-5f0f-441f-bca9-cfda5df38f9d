package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// MemoryRateLimiter 简单的内存限流器
type MemoryRateLimiter struct {
	visitors map[string]*Visitor
	mu       sync.RWMutex
}

// Visitor 访问者信息
type Visitor struct {
	limiter  *TokenBucket
	lastSeen time.Time
}

// TokenBucket 令牌桶
type TokenBucket struct {
	tokens   int
	capacity int
	refill   int
	interval time.Duration
	lastTime time.Time
	mu       sync.Mutex
}

// NewTokenBucket 创建令牌桶
func NewTokenBucket(capacity, refill int, interval time.Duration) *TokenBucket {
	return &TokenBucket{
		tokens:   capacity,
		capacity: capacity,
		refill:   refill,
		interval: interval,
		lastTime: time.Now(),
	}
}

// Allow 检查是否允许请求
func (tb *TokenBucket) Allow() bool {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(tb.lastTime)

	// 添加令牌
	if elapsed >= tb.interval {
		tokensToAdd := int(elapsed/tb.interval) * tb.refill
		tb.tokens = min(tb.capacity, tb.tokens+tokensToAdd)
		tb.lastTime = now
	}

	// 消费令牌
	if tb.tokens > 0 {
		tb.tokens--
		return true
	}

	return false
}

var rateLimiter = &MemoryRateLimiter{
	visitors: make(map[string]*Visitor),
}

// RateLimiterMiddleware 限流中间件
func RateLimiter() gin.HandlerFunc {
	// 清理过期访问者的goroutine
	go cleanupVisitors()

	return func(c *gin.Context) {
		ip := c.ClientIP()

		rateLimiter.mu.Lock()
		visitor, exists := rateLimiter.visitors[ip]
		if !exists {
			// 每个IP每秒最多10个请求，容量20
			visitor = &Visitor{
				limiter:  NewTokenBucket(20, 10, time.Second),
				lastSeen: time.Now(),
			}
			rateLimiter.visitors[ip] = visitor
		} else {
			visitor.lastSeen = time.Now()
		}
		rateLimiter.mu.Unlock()

		if !visitor.limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "请求过于频繁",
				"message": "请稍后再试",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// cleanupVisitors 清理过期的访问者
func cleanupVisitors() {
	for {
		time.Sleep(time.Minute)

		rateLimiter.mu.Lock()
		for ip, visitor := range rateLimiter.visitors {
			if time.Since(visitor.lastSeen) > time.Hour {
				delete(rateLimiter.visitors, ip)
			}
		}
		rateLimiter.mu.Unlock()
	}
}

// AdminAuth 管理员认证中间件（简化版）
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里应该实现真正的管理员认证逻辑
		// 简化版直接通过
		c.Next()
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
