-- 成语迷宫数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS idiom_maze CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE idiom_maze;

-- 用户表
CREATE TABLE users (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信openid',
    unionid VARCHAR(100) COMMENT '微信unionid',
    nickname VARCHAR(100) NOT NULL COMMENT '用户昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0未知，1男，2女',
    city VARCHAR(100) COMMENT '城市',
    province VARCHAR(100) COMMENT '省份',
    country VARCHAR(100) COMMENT '国家',
    total_score INT DEFAULT 0 COMMENT '总分数',
    current_level INT DEFAULT 1 COMMENT '当前关卡',
    unlocked_levels INT DEFAULT 1 COMMENT '已解锁关卡数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_openid (openid),
    INDEX idx_total_score (total_score DESC)
) ENGINE=InnoDB COMMENT='用户表';

-- 成语表
CREATE TABLE idioms (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    idiom VARCHAR(20) NOT NULL COMMENT '成语',
    pinyin VARCHAR(100) COMMENT '拼音',
    meaning TEXT COMMENT '释义',
    example TEXT COMMENT '例句',
    source VARCHAR(200) COMMENT '出处',
    difficulty TINYINT DEFAULT 1 COMMENT '难度等级：1简单，2中等，3困难',
    category VARCHAR(50) COMMENT '分类',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_difficulty (difficulty),
    INDEX idx_category (category),
    INDEX idx_usage_count (usage_count DESC),
    FULLTEXT idx_idiom_meaning (idiom, meaning)
) ENGINE=InnoDB COMMENT='成语表';

-- 关卡表
CREATE TABLE levels (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    level_number INT NOT NULL UNIQUE COMMENT '关卡编号',
    name VARCHAR(100) NOT NULL COMMENT '关卡名称',
    description TEXT COMMENT '关卡描述',
    maze_size TINYINT DEFAULT 7 COMMENT '迷宫大小',
    maze_data JSON COMMENT '迷宫数据',
    target_idioms JSON COMMENT '目标成语列表',
    difficulty TINYINT DEFAULT 1 COMMENT '难度等级',
    min_score INT DEFAULT 0 COMMENT '最低通关分数',
    time_limit INT DEFAULT 300 COMMENT '时间限制（秒）',
    hint_count INT DEFAULT 3 COMMENT '提示次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_level_number (level_number),
    INDEX idx_difficulty (difficulty)
) ENGINE=InnoDB COMMENT='关卡表';

-- 用户游戏记录表
CREATE TABLE user_game_records (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID',
    level_id INT UNSIGNED NOT NULL COMMENT '关卡ID',
    score INT DEFAULT 0 COMMENT '得分',
    stars TINYINT DEFAULT 0 COMMENT '星级评价：0-3星',
    time_used INT DEFAULT 0 COMMENT '用时（秒）',
    hints_used INT DEFAULT 0 COMMENT '使用提示次数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否完成',
    game_data JSON COMMENT '游戏详细数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    INDEX idx_user_level (user_id, level_id),
    INDEX idx_score (score DESC),
    INDEX idx_created_at (created_at DESC)
) ENGINE=InnoDB COMMENT='用户游戏记录表';

-- 用户成就表
CREATE TABLE user_achievements (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID',
    achievement_type VARCHAR(50) NOT NULL COMMENT '成就类型',
    achievement_name VARCHAR(100) NOT NULL COMMENT '成就名称',
    description TEXT COMMENT '成就描述',
    icon_url VARCHAR(500) COMMENT '成就图标',
    reward_score INT DEFAULT 0 COMMENT '奖励分数',
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '解锁时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_achievement_type (achievement_type),
    INDEX idx_unlocked_at (unlocked_at DESC)
) ENGINE=InnoDB COMMENT='用户成就表';

-- 排行榜表
CREATE TABLE leaderboards (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID',
    leaderboard_type VARCHAR(50) NOT NULL COMMENT '排行榜类型：total_score, level_score, speed',
    score INT NOT NULL COMMENT '分数',
    level_id INT UNSIGNED COMMENT '关卡ID（关卡排行榜用）',
    rank_position INT COMMENT '排名位置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_type_score (leaderboard_type, score DESC),
    INDEX idx_level_score (level_id, score DESC),
    INDEX idx_user_type (user_id, leaderboard_type)
) ENGINE=InnoDB COMMENT='排行榜表';

-- 游戏配置表
CREATE TABLE game_configs (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    config_type VARCHAR(50) DEFAULT 'string' COMMENT '配置类型：string, int, json, boolean',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB COMMENT='游戏配置表';

-- 插入初始成语数据
INSERT INTO idioms (idiom, pinyin, meaning, difficulty, category) VALUES
('画蛇添足', 'huà shé tiān zú', '画蛇时给蛇添上脚。比喻做了多余的事，非但无益，反而不合适。', 1, '寓言故事'),
('守株待兔', 'shǒu zhū dài tù', '原比喻希图不经过努力而得到成功的侥幸心理。现也比喻死守狭隘经验，不知变通。', 1, '寓言故事'),
('亡羊补牢', 'wáng yáng bǔ láo', '羊逃跑了再去修补羊圈，还不算晚。比喻出了问题以后想办法补救，可以防止继续受损失。', 1, '寓言故事'),
('杯弓蛇影', 'bēi gōng shé yǐng', '将映在酒杯里的弓影误认为蛇。比喻因疑神疑鬼而引起恐惧。', 2, '历史典故'),
('刻舟求剑', 'kè zhōu qiú jiàn', '比喻不懂事物已发展变化而仍静止地看问题。', 1, '寓言故事'),
('掩耳盗铃', 'yǎn ěr dào líng', '偷铃铛怕别人听见而捂住自己的耳朵。比喻自己欺骗自己，明明掩盖不住的事情偏要想法子掩盖。', 1, '寓言故事'),
('买椟还珠', 'mǎi dú huán zhū', '买下木匣，退还了珍珠。比喻没有眼力，取舍不当。', 2, '历史典故'),
('南辕北辙', 'nán yuán běi zhé', '想往南而车子却向北行。比喻行动和目的正好相反。', 2, '寓言故事');

-- 插入初始关卡数据
INSERT INTO levels (level_number, name, description, maze_size, difficulty, target_idioms) VALUES
(1, '初出茅庐', '欢迎来到成语迷宫！让我们从简单的成语开始吧。', 5, 1, '["画蛇添足", "守株待兔"]'),
(2, '小试牛刀', '继续挑战更多有趣的成语！', 6, 1, '["亡羊补牢", "刻舟求剑"]'),
(3, '渐入佳境', '难度开始提升，准备好了吗？', 7, 2, '["杯弓蛇影", "掩耳盗铃"]'),
(4, '登堂入室', '挑战更复杂的成语组合！', 7, 2, '["买椟还珠", "南辕北辙"]'),
(5, '炉火纯青', '恭喜你已经是成语高手了！', 8, 3, '["画蛇添足", "杯弓蛇影", "买椟还珠"]');

-- 插入游戏配置
INSERT INTO game_configs (config_key, config_value, description, config_type) VALUES
('max_hints_per_level', '3', '每关最大提示次数', 'int'),
('hint_score_penalty', '10', '使用提示扣分', 'int'),
('time_bonus_factor', '1.5', '时间奖励系数', 'float'),
('star_thresholds', '{"1": 60, "2": 80, "3": 95}', '星级评价阈值', 'json'),
('daily_bonus_score', '50', '每日登录奖励分数', 'int'),
('share_bonus_score', '20', '分享奖励分数', 'int');
