#!/bin/bash

# 成语迷宫后端启动脚本

echo "🚀 启动成语迷宫后端服务..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装，请先安装Go 1.21或更高版本"
    exit 1
fi

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env文件不存在，复制.env.example为.env"
    cp .env.example .env
    echo "📝 请编辑.env文件配置数据库连接信息"
    exit 1
fi

# 安装依赖
echo "📦 安装Go依赖..."
go mod tidy

# 检查数据库连接
echo "🔍 检查数据库连接..."
go run scripts/check_db.go

if [ $? -ne 0 ]; then
    echo "❌ 数据库连接失败，请检查配置"
    exit 1
fi

# 运行数据库迁移
echo "🗄️  执行数据库迁移..."
go run scripts/migrate.go

# 启动服务
echo "🎯 启动API服务..."
go run main.go
