package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"

	"idiom-maze/config"
	"idiom-maze/database"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.Load()

	fmt.Println("🔍 检查数据库连接...")
	fmt.Printf("MySQL: %s:%d\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Printf("Redis: %s:%d\n", cfg.Redis.Host, cfg.Redis.Port)

	// 测试MySQL连接
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		fmt.Printf("❌ MySQL连接失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("✅ MySQL连接成功")

	// 测试Redis连接
	rdb, err := database.InitializeRedis(cfg.Redis)
	if err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("✅ Redis连接成功")

	// 关闭连接
	database.Close()

	fmt.Println("🎉 所有数据库连接正常！")
}
