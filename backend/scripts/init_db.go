package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func main() {
	// 数据库连接信息
	host := "************"
	port := "3306"
	username := "root"
	password := "minMIN123@"
	dbname := "idiom_maze"

	// 连接MySQL（不指定数据库）
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/", username, password, host, port)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("连接MySQL失败:", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatal("MySQL连接测试失败:", err)
	}
	fmt.Println("✅ MySQL连接成功")

	// 创建数据库
	createDBSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbname)
	_, err = db.Exec(createDBSQL)
	if err != nil {
		log.Fatal("创建数据库失败:", err)
	}
	fmt.Printf("✅ 数据库 %s 创建成功\n", dbname)

	// 切换到目标数据库
	_, err = db.Exec(fmt.Sprintf("USE %s", dbname))
	if err != nil {
		log.Fatal("切换数据库失败:", err)
	}

	// 读取SQL文件
	sqlFile := "database/schema.sql"
	if len(os.Args) > 1 {
		sqlFile = os.Args[1]
	}

	sqlContent, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		log.Fatal("读取SQL文件失败:", err)
	}

	// 处理SQL内容，移除注释和空行
	lines := strings.Split(string(sqlContent), "\n")
	var cleanSQL strings.Builder

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "--") {
			continue
		}
		cleanSQL.WriteString(line + " ")
	}

	// 分割SQL语句
	statements := strings.Split(cleanSQL.String(), ";")

	fmt.Println("🗄️  开始执行SQL脚本...")

	for i, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" {
			continue
		}

		_, err = db.Exec(statement)
		if err != nil {
			fmt.Printf("⚠️  执行第%d条SQL语句失败: %v\n", i+1, err)
			fmt.Printf("SQL: %s\n", statement[:min(len(statement), 100)]+"...")
			// 继续执行其他语句
			continue
		} else {
			fmt.Printf("✅ 执行第%d条SQL语句成功\n", i+1)
		}
	}

	fmt.Println("🎉 数据库初始化完成！")

	// 验证表是否创建成功
	tables := []string{"users", "idioms", "levels", "user_game_records", "user_achievements", "leaderboards", "game_configs"}
	fmt.Println("📋 验证表结构...")

	for _, table := range tables {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '%s' AND table_name = '%s'", dbname, table)).Scan(&count)
		if err != nil {
			fmt.Printf("❌ 验证表 %s 失败: %v\n", table, err)
		} else if count > 0 {
			fmt.Printf("✅ 表 %s 创建成功\n", table)
		} else {
			fmt.Printf("❌ 表 %s 不存在\n", table)
		}
	}
}
