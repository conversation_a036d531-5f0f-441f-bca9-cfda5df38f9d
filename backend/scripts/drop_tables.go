package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 数据库连接信息
	host := "************"
	port := "3306"
	username := "root"
	password := "minMIN123@"
	dbname := "idiom_maze"

	// 连接MySQL
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, host, port, dbname)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("连接MySQL失败:", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatal("MySQL连接测试失败:", err)
	}
	fmt.Println("✅ MySQL连接成功")

	// 删除所有表
	tables := []string{
		"user_achievements",
		"user_game_records", 
		"leaderboards",
		"game_configs",
		"levels",
		"idioms",
		"users",
	}

	fmt.Println("🗑️  开始删除表...")
	
	// 先禁用外键检查
	_, err = db.Exec("SET FOREIGN_KEY_CHECKS = 0")
	if err != nil {
		log.Printf("禁用外键检查失败: %v", err)
	}

	for _, table := range tables {
		_, err = db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table))
		if err != nil {
			fmt.Printf("⚠️  删除表 %s 失败: %v\n", table, err)
		} else {
			fmt.Printf("✅ 删除表 %s 成功\n", table)
		}
	}

	// 重新启用外键检查
	_, err = db.Exec("SET FOREIGN_KEY_CHECKS = 1")
	if err != nil {
		log.Printf("启用外键检查失败: %v", err)
	}

	fmt.Println("🎉 所有表删除完成！")
}
