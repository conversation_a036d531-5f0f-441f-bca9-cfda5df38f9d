package models

import (
	"time"
)

// Level 关卡模型
type Level struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	LevelNumber  int       `json:"level_number" gorm:"uniqueIndex;not null"`
	Name         string    `json:"name" gorm:"size:100;not null"`
	Description  string    `json:"description" gorm:"type:text"`
	MazeSize     int8      `json:"maze_size" gorm:"default:7"`
	MazeData     string    `json:"maze_data" gorm:"type:json"`
	TargetIdioms string    `json:"target_idioms" gorm:"type:json"`
	Difficulty   int8      `json:"difficulty" gorm:"default:1;index:idx_difficulty"`
	MinScore     int       `json:"min_score" gorm:"default:0"`
	TimeLimit    int       `json:"time_limit" gorm:"default:300"`
	HintCount    int       `json:"hint_count" gorm:"default:3"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Level) TableName() string {
	return "levels"
}

// Idiom 成语模型
type Idiom struct {
	ID         uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Idiom      string    `json:"idiom" gorm:"size:20;not null"`
	Pinyin     string    `json:"pinyin" gorm:"size:100"`
	Meaning    string    `json:"meaning" gorm:"type:text"`
	Example    string    `json:"example" gorm:"type:text"`
	Source     string    `json:"source" gorm:"size:200"`
	Difficulty int8      `json:"difficulty" gorm:"default:1;index:idx_difficulty"`
	Category   string    `json:"category" gorm:"size:50;index:idx_category"`
	UsageCount int       `json:"usage_count" gorm:"default:0;index:idx_usage_count"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Idiom) TableName() string {
	return "idioms"
}

// Leaderboard 排行榜模型
type Leaderboard struct {
	ID              uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID          uint      `json:"user_id" gorm:"not null;index:idx_user_type"`
	LeaderboardType string    `json:"leaderboard_type" gorm:"size:50;not null;index:idx_type_score"`
	Score           int       `json:"score" gorm:"not null;index:idx_type_score"`
	LevelID         uint      `json:"level_id" gorm:"index:idx_level_score"`
	RankPosition    int       `json:"rank_position"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`

	// 关联
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (Leaderboard) TableName() string {
	return "leaderboards"
}

// GameConfig 游戏配置模型
type GameConfig struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	ConfigKey   string    `json:"config_key" gorm:"size:100;uniqueIndex;not null"`
	ConfigValue string    `json:"config_value" gorm:"type:text;not null"`
	Description string    `json:"description" gorm:"type:text"`
	ConfigType  string    `json:"config_type" gorm:"size:50;default:'string'"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (GameConfig) TableName() string {
	return "game_configs"
}

// 游戏相关的请求和响应结构

// StartGameRequest 开始游戏请求
type StartGameRequest struct {
	LevelID uint `json:"level_id" binding:"required"`
}

// SaveProgressRequest 保存进度请求
type SaveProgressRequest struct {
	LevelID        uint                   `json:"level_id" binding:"required"`
	Score          int                    `json:"score"`
	TimeUsed       int                    `json:"time_used"`
	HintsUsed      int                    `json:"hints_used"`
	PlayerPosition map[string]int         `json:"player_position"`
	GameData       map[string]interface{} `json:"game_data"`
}

// CompleteGameRequest 完成游戏请求
type CompleteGameRequest struct {
	LevelID        uint    `json:"level_id" binding:"required"`
	TimeUsed       int     `json:"time_used" binding:"required"`
	CompletionRate float64 `json:"completion_rate" binding:"required"`
}

// GameSession 游戏会话
type GameSession struct {
	UserID         uint                   `json:"user_id"`
	LevelID        uint                   `json:"level_id"`
	MazeData       *MazeData              `json:"maze_data"`
	StartTime      time.Time              `json:"start_time"`
	Score          int                    `json:"score"`
	TimeUsed       int                    `json:"time_used"`
	HintsUsed      int                    `json:"hints_used"`
	PlayerPosition map[string]int         `json:"player_position"`
	IsCompleted    bool                   `json:"is_completed"`
	GameData       map[string]interface{} `json:"game_data"`
}

// MazeData 迷宫数据
type MazeData struct {
	Size  int          `json:"size"`
	Cells [][]MazeCell `json:"cells"`
}

// MazeCell 迷宫格子
type MazeCell struct {
	Row       int    `json:"row"`
	Col       int    `json:"col"`
	Type      string `json:"type"`      // "wall", "path", "visited"
	Character string `json:"character"` // 成语字符
	IsStart   bool   `json:"is_start"`
	IsEnd     bool   `json:"is_end"`
}

// GameResult 游戏结果
type GameResult struct {
	Score        int           `json:"score"`
	Stars        int           `json:"stars"`
	TimeUsed     int           `json:"time_used"`
	Achievements []Achievement `json:"achievements"`
	NextLevel    uint          `json:"next_level"`
}

// Achievement 成就
type Achievement struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IconURL     string `json:"icon_url"`
	RewardScore int    `json:"reward_score"`
}
