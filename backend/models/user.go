package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID             uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	OpenID         string    `json:"openid" gorm:"uniqueIndex;size:100;not null"`
	UnionID        string    `json:"unionid" gorm:"size:100"`
	Nickname       string    `json:"nickname" gorm:"size:100;not null"`
	AvatarURL      string    `json:"avatar_url" gorm:"size:500"`
	Gender         int8      `json:"gender" gorm:"default:0"`
	City           string    `json:"city" gorm:"size:100"`
	Province       string    `json:"province" gorm:"size:100"`
	Country        string    `json:"country" gorm:"size:100"`
	TotalScore     int       `json:"total_score" gorm:"default:0;index:idx_total_score"`
	CurrentLevel   int       `json:"current_level" gorm:"default:1"`
	UnlockedLevels int       `json:"unlocked_levels" gorm:"default:1"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// UserGameRecord 用户游戏记录模型
type UserGameRecord struct {
	ID             uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID         uint64    `json:"user_id" gorm:"not null;index:idx_user_level"`
	LevelID        uint64    `json:"level_id" gorm:"not null;index:idx_user_level"`
	Score          int       `json:"score" gorm:"default:0;index:idx_score"`
	Stars          int8      `json:"stars" gorm:"default:0"`
	TimeUsed       int       `json:"time_used" gorm:"default:0"`
	HintsUsed      int       `json:"hints_used" gorm:"default:0"`
	CompletionRate float64   `json:"completion_rate" gorm:"type:decimal(5,2);default:0.00"`
	IsCompleted    bool      `json:"is_completed" gorm:"default:false"`
	GameData       string    `json:"game_data" gorm:"type:json"`
	CreatedAt      time.Time `json:"created_at" gorm:"index:idx_created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	
	// 关联
	User  User  `json:"user" gorm:"foreignKey:UserID"`
	Level Level `json:"level" gorm:"foreignKey:LevelID"`
}

// TableName 指定表名
func (UserGameRecord) TableName() string {
	return "user_game_records"
}

// UserAchievement 用户成就模型
type UserAchievement struct {
	ID              uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID          uint64    `json:"user_id" gorm:"not null;index:idx_user_id"`
	AchievementType string    `json:"achievement_type" gorm:"size:50;not null;index:idx_achievement_type"`
	AchievementName string    `json:"achievement_name" gorm:"size:100;not null"`
	Description     string    `json:"description" gorm:"type:text"`
	IconURL         string    `json:"icon_url" gorm:"size:500"`
	RewardScore     int       `json:"reward_score" gorm:"default:0"`
	UnlockedAt      time.Time `json:"unlocked_at" gorm:"index:idx_unlocked_at"`
	
	// 关联
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (UserAchievement) TableName() string {
	return "user_achievements"
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Code     string `json:"code" binding:"required"`
	Nickname string `json:"nickname" binding:"required"`
	Avatar   string `json:"avatar"`
	Gender   int8   `json:"gender"`
	City     string `json:"city"`
	Province string `json:"province"`
	Country  string `json:"country"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Gender   int8   `json:"gender"`
	City     string `json:"city"`
	Province string `json:"province"`
	Country  string `json:"country"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID             uint64    `json:"id"`
	Nickname       string    `json:"nickname"`
	AvatarURL      string    `json:"avatar_url"`
	TotalScore     int       `json:"total_score"`
	CurrentLevel   int       `json:"current_level"`
	UnlockedLevels int       `json:"unlocked_levels"`
	CreatedAt      time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:             u.ID,
		Nickname:       u.Nickname,
		AvatarURL:      u.AvatarURL,
		TotalScore:     u.TotalScore,
		CurrentLevel:   u.CurrentLevel,
		UnlockedLevels: u.UnlockedLevels,
		CreatedAt:      u.CreatedAt,
	}
}
