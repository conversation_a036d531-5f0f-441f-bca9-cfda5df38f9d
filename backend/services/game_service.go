package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/models"
	"idiom-maze/utils"
)

// GameService 游戏服务
type GameService struct {
	db  *gorm.DB
	rdb *redis.Client
	ctx context.Context
}

// NewGameService 创建游戏服务实例
func NewGameService(db *gorm.DB, rdb *redis.Client) *GameService {
	return &GameService{
		db:  db,
		rdb: rdb,
		ctx: context.Background(),
	}
}

// GetLevels 获取关卡列表
func (s *GameService) GetLevels(page, pageSize int, difficulty string) ([]models.Level, int64, error) {
	var levels []models.Level
	var total int64

	query := s.db.Model(&models.Level{}).Where("is_active = ?", true)

	if difficulty != "" {
		query = query.Where("difficulty = ?", difficulty)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("level_number ASC").Find(&levels).Error; err != nil {
		return nil, 0, err
	}

	return levels, total, nil
}

// GetLevel 获取单个关卡
func (s *GameService) GetLevel(levelID uint) (*models.Level, error) {
	var level models.Level
	if err := s.db.Where("id = ? AND is_active = ?", levelID, true).First(&level).Error; err != nil {
		return nil, err
	}
	return &level, nil
}

// StartGame 开始游戏
func (s *GameService) StartGame(userID, levelID uint) (*models.GameSession, error) {
	// 获取关卡信息
	level, err := s.GetLevel(levelID)
	if err != nil {
		return nil, fmt.Errorf("关卡不存在: %v", err)
	}

	// 检查用户是否已解锁该关卡
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	if int(level.LevelNumber) > user.UnlockedLevels {
		return nil, fmt.Errorf("关卡未解锁")
	}

	// 生成迷宫数据
	mazeData, err := s.GenerateMaze(int(level.MazeSize), int(level.Difficulty))
	if err != nil {
		return nil, fmt.Errorf("生成迷宫失败: %v", err)
	}

	// 创建游戏会话
	session := &models.GameSession{
		UserID:      userID,
		LevelID:     levelID,
		MazeData:    mazeData,
		StartTime:   time.Now(),
		Score:       0,
		HintsUsed:   0,
		IsCompleted: false,
		GameData:    make(map[string]interface{}),
	}

	// 保存到Redis缓存
	sessionKey := fmt.Sprintf("game_session:%d:%d", userID, levelID)
	sessionData, _ := json.Marshal(session)
	if err := s.rdb.Set(s.ctx, sessionKey, sessionData, time.Hour*2).Err(); err != nil {
		return nil, fmt.Errorf("保存游戏会话失败: %v", err)
	}

	return session, nil
}

// SaveProgress 保存游戏进度
func (s *GameService) SaveProgress(userID uint, req *models.SaveProgressRequest) error {
	sessionKey := fmt.Sprintf("game_session:%d:%d", userID, req.LevelID)

	// 从Redis获取当前会话
	sessionData, err := s.rdb.Get(s.ctx, sessionKey).Result()
	if err != nil {
		return fmt.Errorf("游戏会话不存在: %v", err)
	}

	var session models.GameSession
	if err := json.Unmarshal([]byte(sessionData), &session); err != nil {
		return fmt.Errorf("解析游戏会话失败: %v", err)
	}

	// 更新会话数据
	session.Score = req.Score
	session.TimeUsed = req.TimeUsed
	session.HintsUsed = req.HintsUsed
	session.PlayerPosition = req.PlayerPosition
	session.GameData = req.GameData

	// 保存回Redis
	updatedData, _ := json.Marshal(session)
	if err := s.rdb.Set(s.ctx, sessionKey, updatedData, time.Hour*2).Err(); err != nil {
		return fmt.Errorf("保存进度失败: %v", err)
	}

	return nil
}

// GetProgress 获取游戏进度
func (s *GameService) GetProgress(userID, levelID uint) (*models.GameSession, error) {
	sessionKey := fmt.Sprintf("game_session:%d:%d", userID, levelID)

	sessionData, err := s.rdb.Get(s.ctx, sessionKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("游戏进度不存在")
		}
		return nil, fmt.Errorf("获取游戏进度失败: %v", err)
	}

	var session models.GameSession
	if err := json.Unmarshal([]byte(sessionData), &session); err != nil {
		return nil, fmt.Errorf("解析游戏进度失败: %v", err)
	}

	return &session, nil
}

// CompleteGame 完成游戏
func (s *GameService) CompleteGame(userID uint, req *models.CompleteGameRequest) (*models.GameResult, error) {
	// 获取游戏会话
	session, err := s.GetProgress(userID, req.LevelID)
	if err != nil {
		return nil, err
	}

	// 计算最终分数和星级
	finalScore := s.calculateFinalScore(session, req)
	stars := s.calculateStars(finalScore, session.HintsUsed, req.TimeUsed)

	// 保存游戏记录到数据库
	record := &models.UserGameRecord{
		UserID:         userID,
		LevelID:        req.LevelID,
		Score:          finalScore,
		Stars:          int8(stars),
		TimeUsed:       req.TimeUsed,
		HintsUsed:      session.HintsUsed,
		CompletionRate: req.CompletionRate,
		IsCompleted:    true,
		GameData:       utils.ToJSONString(session.GameData),
	}

	if err := s.db.Create(record).Error; err != nil {
		return nil, fmt.Errorf("保存游戏记录失败: %v", err)
	}

	// 更新用户数据
	if err := s.updateUserProgress(userID, req.LevelID, finalScore, stars); err != nil {
		return nil, fmt.Errorf("更新用户进度失败: %v", err)
	}

	// 检查成就
	achievements := s.checkAchievements(userID, record)

	// 清除Redis中的游戏会话
	sessionKey := fmt.Sprintf("game_session:%d:%d", userID, req.LevelID)
	s.rdb.Del(s.ctx, sessionKey)

	result := &models.GameResult{
		Score:        finalScore,
		Stars:        stars,
		TimeUsed:     req.TimeUsed,
		Achievements: achievements,
		NextLevel:    req.LevelID + 1,
	}

	return result, nil
}

// GenerateMaze 生成迷宫
func (s *GameService) GenerateMaze(size, difficulty int) (*models.MazeData, error) {
	maze := &models.MazeData{
		Size:  size,
		Cells: make([][]models.MazeCell, size),
	}

	// 初始化迷宫
	for i := 0; i < size; i++ {
		maze.Cells[i] = make([]models.MazeCell, size)
		for j := 0; j < size; j++ {
			maze.Cells[i][j] = models.MazeCell{
				Row:       i,
				Col:       j,
				Type:      "path",
				Character: "",
				IsStart:   i == 0 && j == 0,
				IsEnd:     i == size-1 && j == size-1,
			}
		}
	}

	// 根据难度生成墙壁
	wallDensity := 0.2 + float64(difficulty-1)*0.1
	s.generateWalls(maze, wallDensity)

	// 确保路径连通性
	s.ensurePathConnectivity(maze)

	// 放置成语字符
	s.placeIdiomCharacters(maze, difficulty)

	return maze, nil
}

// generateWalls 生成墙壁
func (s *GameService) generateWalls(maze *models.MazeData, density float64) {
	rand.Seed(time.Now().UnixNano())

	for i := 1; i < maze.Size-1; i++ {
		for j := 1; j < maze.Size-1; j++ {
			if rand.Float64() < density {
				maze.Cells[i][j].Type = "wall"
			}
		}
	}
}

// ensurePathConnectivity 确保路径连通性
func (s *GameService) ensurePathConnectivity(maze *models.MazeData) {
	// 使用简单的路径确保算法
	// 从起点到终点确保至少有一条路径

	// 这里实现一个简化版的路径确保算法
	// 实际项目中应该使用更复杂的迷宫生成算法

	size := maze.Size

	// 确保右边界和下边界有路径
	for i := 0; i < size-1; i++ {
		if maze.Cells[i][size-1].Type == "wall" && maze.Cells[i+1][size-1].Type == "wall" {
			maze.Cells[i][size-1].Type = "path"
		}
		if maze.Cells[size-1][i].Type == "wall" && maze.Cells[size-1][i+1].Type == "wall" {
			maze.Cells[size-1][i].Type = "path"
		}
	}
}

// placeIdiomCharacters 放置成语字符
func (s *GameService) placeIdiomCharacters(maze *models.MazeData, difficulty int) {
	// 获取可用的路径格子
	var pathCells []models.MazeCell
	for i := 0; i < maze.Size; i++ {
		for j := 0; j < maze.Size; j++ {
			if maze.Cells[i][j].Type == "path" && !maze.Cells[i][j].IsStart && !maze.Cells[i][j].IsEnd {
				pathCells = append(pathCells, maze.Cells[i][j])
			}
		}
	}

	// 根据难度选择成语
	idioms := s.getIdiomsForDifficulty(difficulty)

	// 随机放置成语字符
	rand.Seed(time.Now().UnixNano())
	charIndex := 0

	for _, idiom := range idioms {
		for _, char := range idiom {
			if charIndex < len(pathCells) {
				cell := &pathCells[charIndex]
				maze.Cells[cell.Row][cell.Col].Character = string(char)
				charIndex++
			}
		}
	}
}

// getIdiomsForDifficulty 根据难度获取成语
func (s *GameService) getIdiomsForDifficulty(difficulty int) []string {
	allIdioms := []string{"画蛇添足", "守株待兔", "亡羊补牢", "杯弓蛇影", "刻舟求剑", "掩耳盗铃"}

	count := 1 + difficulty/2
	if count > len(allIdioms) {
		count = len(allIdioms)
	}

	return allIdioms[:count]
}

// calculateFinalScore 计算最终分数
func (s *GameService) calculateFinalScore(session *models.GameSession, req *models.CompleteGameRequest) int {
	baseScore := session.Score

	// 时间奖励
	timeBonus := max(0, 300-req.TimeUsed) // 5分钟内完成有时间奖励

	// 提示惩罚
	hintPenalty := session.HintsUsed * 10

	finalScore := baseScore + timeBonus - hintPenalty
	return max(0, finalScore)
}

// calculateStars 计算星级评价
func (s *GameService) calculateStars(score, hintsUsed, timeUsed int) int {
	if score >= 200 && hintsUsed == 0 && timeUsed <= 180 {
		return 3
	} else if score >= 150 && hintsUsed <= 1 && timeUsed <= 300 {
		return 2
	} else {
		return 1
	}
}

// updateUserProgress 更新用户进度
func (s *GameService) updateUserProgress(userID, levelID uint, score, stars int) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	// 更新总分数
	user.TotalScore += score

	// 更新当前关卡和解锁关卡
	if int(levelID) >= user.CurrentLevel {
		user.CurrentLevel = int(levelID)
		user.UnlockedLevels = max(user.UnlockedLevels, int(levelID)+1)
	}

	return s.db.Save(&user).Error
}

// checkAchievements 检查成就
func (s *GameService) checkAchievements(userID uint, record *models.UserGameRecord) []models.Achievement {
	var achievements []models.Achievement

	// 这里实现成就检查逻辑
	// 例如：首次完成、完美通关、速度通关等

	return achievements
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
