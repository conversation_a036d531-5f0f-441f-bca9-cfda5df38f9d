package services

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/models"
)

// UserService 用户服务
type UserService struct {
	db  *gorm.DB
	rdb *redis.Client
	ctx context.Context
}

// NewUserService 创建用户服务实例
func NewUserService(db *gorm.DB, rdb *redis.Client) *UserService {
	return &UserService{
		db:  db,
		rdb: rdb,
		ctx: context.Background(),
	}
}

// Login 用户登录
func (s *UserService) Login(req *models.CreateUserRequest) (*models.User, string, error) {
	// 这里应该调用微信API验证code，获取openid
	// 简化版直接使用code作为openid
	openid := req.Code

	var user models.User
	err := s.db.Where("openid = ?", openid).First(&user).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户不存在，创建新用户
			user = models.User{
				OpenID:         openid,
				Nickname:       req.Nickname,
				AvatarURL:      req.Avatar,
				Gender:         req.Gender,
				City:           req.City,
				Province:       req.Province,
				Country:        req.Country,
				TotalScore:     0,
				CurrentLevel:   1,
				UnlockedLevels: 1,
			}
			
			if err := s.db.Create(&user).Error; err != nil {
				return nil, "", fmt.Errorf("创建用户失败: %v", err)
			}
		} else {
			return nil, "", fmt.Errorf("查询用户失败: %v", err)
		}
	}

	// 生成JWT token（简化版）
	token := fmt.Sprintf("token_%d_%d", user.ID, time.Now().Unix())

	// 将token存储到Redis
	tokenKey := fmt.Sprintf("user_token:%s", token)
	if err := s.rdb.Set(s.ctx, tokenKey, user.ID, time.Hour*24*7).Err(); err != nil {
		return nil, "", fmt.Errorf("保存token失败: %v", err)
	}

	return &user, token, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(userID uint64) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(userID uint64, req *models.UpdateUserRequest) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}

	// 更新字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Avatar != "" {
		user.AvatarURL = req.Avatar
	}
	if req.Gender != 0 {
		user.Gender = req.Gender
	}
	if req.City != "" {
		user.City = req.City
	}
	if req.Province != "" {
		user.Province = req.Province
	}
	if req.Country != "" {
		user.Country = req.Country
	}

	if err := s.db.Save(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

// GetUserStats 获取用户统计信息
func (s *UserService) GetUserStats(userID uint64) (map[string]interface{}, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}

	// 获取游戏记录统计
	var totalGames int64
	var completedGames int64
	var totalStars int64

	s.db.Model(&models.UserGameRecord{}).Where("user_id = ?", userID).Count(&totalGames)
	s.db.Model(&models.UserGameRecord{}).Where("user_id = ? AND is_completed = ?", userID, true).Count(&completedGames)
	s.db.Model(&models.UserGameRecord{}).Where("user_id = ?", userID).Select("COALESCE(SUM(stars), 0)").Scan(&totalStars)

	// 获取成就数量
	var achievementCount int64
	s.db.Model(&models.UserAchievement{}).Where("user_id = ?", userID).Count(&achievementCount)

	stats := map[string]interface{}{
		"user_info": user.ToResponse(),
		"game_stats": map[string]interface{}{
			"total_games":     totalGames,
			"completed_games": completedGames,
			"total_stars":     totalStars,
			"achievements":    achievementCount,
		},
	}

	return stats, nil
}
