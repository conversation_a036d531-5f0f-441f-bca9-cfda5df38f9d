package services

import (
	"context"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/models"
)

// LeaderboardService 排行榜服务
type LeaderboardService struct {
	db  *gorm.DB
	rdb *redis.Client
	ctx context.Context
}

// NewLeaderboardService 创建排行榜服务实例
func NewLeaderboardService(db *gorm.DB, rdb *redis.Client) *LeaderboardService {
	return &LeaderboardService{
		db:  db,
		rdb: rdb,
		ctx: context.Background(),
	}
}

// GetTotalScoreLeaderboard 获取总分排行榜
func (s *LeaderboardService) GetTotalScoreLeaderboard(page, pageSize int) ([]map[string]interface{}, int64, error) {
	var results []map[string]interface{}
	var total int64

	// 获取总数
	if err := s.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	rows, err := s.db.Table("users").
		Select("id, nickname, avatar_url, total_score, current_level").
		Order("total_score DESC, id ASC").
		Offset(offset).
		Limit(pageSize).
		Rows()

	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	rank := offset + 1
	for rows.Next() {
		var id uint64
		var nickname, avatarURL string
		var totalScore, currentLevel int

		if err := rows.Scan(&id, &nickname, &avatarURL, &totalScore, &currentLevel); err != nil {
			continue
		}

		results = append(results, map[string]interface{}{
			"rank":          rank,
			"user_id":       id,
			"nickname":      nickname,
			"avatar_url":    avatarURL,
			"total_score":   totalScore,
			"current_level": currentLevel,
		})
		rank++
	}

	return results, total, nil
}

// GetLevelLeaderboard 获取关卡排行榜
func (s *LeaderboardService) GetLevelLeaderboard(levelID uint64, page, pageSize int) ([]map[string]interface{}, int64, error) {
	var results []map[string]interface{}
	var total int64

	// 获取总数
	if err := s.db.Model(&models.UserGameRecord{}).Where("level_id = ? AND is_completed = ?", levelID, true).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	rows, err := s.db.Table("user_game_records").
		Select("user_game_records.score, user_game_records.stars, user_game_records.time_used, users.id, users.nickname, users.avatar_url").
		Joins("JOIN users ON users.id = user_game_records.user_id").
		Where("user_game_records.level_id = ? AND user_game_records.is_completed = ?", levelID, true).
		Order("user_game_records.score DESC, user_game_records.time_used ASC").
		Offset(offset).
		Limit(pageSize).
		Rows()

	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	rank := offset + 1
	for rows.Next() {
		var score, stars, timeUsed int
		var userID uint64
		var nickname, avatarURL string

		if err := rows.Scan(&score, &stars, &timeUsed, &userID, &nickname, &avatarURL); err != nil {
			continue
		}

		results = append(results, map[string]interface{}{
			"rank":       rank,
			"user_id":    userID,
			"nickname":   nickname,
			"avatar_url": avatarURL,
			"score":      score,
			"stars":      stars,
			"time_used":  timeUsed,
		})
		rank++
	}

	return results, total, nil
}

// GetSpeedLeaderboard 获取速度排行榜
func (s *LeaderboardService) GetSpeedLeaderboard(page, pageSize int) ([]map[string]interface{}, int64, error) {
	var results []map[string]interface{}

	// 分页查询
	offset := (page - 1) * pageSize
	rows, err := s.db.Table("user_game_records").
		Select("MIN(user_game_records.time_used) as best_time, users.id, users.nickname, users.avatar_url, COUNT(*) as completed_levels").
		Joins("JOIN users ON users.id = user_game_records.user_id").
		Where("user_game_records.is_completed = ?", true).
		Group("users.id").
		Order("best_time ASC").
		Offset(offset).
		Limit(pageSize).
		Rows()

	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	rank := offset + 1
	for rows.Next() {
		var bestTime, completedLevels int
		var userID uint64
		var nickname, avatarURL string

		if err := rows.Scan(&bestTime, &userID, &nickname, &avatarURL, &completedLevels); err != nil {
			continue
		}

		results = append(results, map[string]interface{}{
			"rank":             rank,
			"user_id":          userID,
			"nickname":         nickname,
			"avatar_url":       avatarURL,
			"best_time":        bestTime,
			"completed_levels": completedLevels,
		})
		rank++
	}

	return results, int64(len(results)), nil
}

// GetUserRanking 获取用户排名
func (s *LeaderboardService) GetUserRanking(userID uint64) (map[string]interface{}, error) {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}

	// 获取总分排名
	var totalScoreRank int64
	s.db.Model(&models.User{}).Where("total_score > ?", user.TotalScore).Count(&totalScoreRank)
	totalScoreRank++ // 排名从1开始

	// 获取最佳时间
	var bestTime int
	s.db.Model(&models.UserGameRecord{}).
		Where("user_id = ? AND is_completed = ?", userID, true).
		Select("MIN(time_used)").
		Scan(&bestTime)

	// 获取速度排名
	var speedRank int64
	if bestTime > 0 {
		s.db.Table("user_game_records").
			Select("COUNT(DISTINCT user_id)").
			Where("is_completed = ? AND time_used < ?", true, bestTime).
			Scan(&speedRank)
		speedRank++
	}

	ranking := map[string]interface{}{
		"user_info": user.ToResponse(),
		"rankings": map[string]interface{}{
			"total_score_rank": totalScoreRank,
			"speed_rank":       speedRank,
			"best_time":        bestTime,
		},
	}

	return ranking, nil
}
