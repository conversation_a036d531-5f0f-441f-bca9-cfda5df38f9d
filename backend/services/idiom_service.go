package services

import (
	"context"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"idiom-maze/models"
)

// IdiomService 成语服务
type IdiomService struct {
	db  *gorm.DB
	rdb *redis.Client
	ctx context.Context
}

// NewIdiomService 创建成语服务实例
func NewIdiomService(db *gorm.DB, rdb *redis.Client) *IdiomService {
	return &IdiomService{
		db:  db,
		rdb: rdb,
		ctx: context.Background(),
	}
}

// GetIdioms 获取成语列表
func (s *IdiomService) GetIdioms(page, pageSize int, difficulty, category string) ([]models.Idiom, int64, error) {
	var idioms []models.Idiom
	var total int64

	query := s.db.Model(&models.Idiom{})

	if difficulty != "" {
		query = query.Where("difficulty = ?", difficulty)
	}

	if category != "" {
		query = query.Where("category = ?", category)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("usage_count DESC, id ASC").Find(&idioms).Error; err != nil {
		return nil, 0, err
	}

	return idioms, total, nil
}

// GetIdiom 获取单个成语
func (s *IdiomService) GetIdiom(idiomID uint64) (*models.Idiom, error) {
	var idiom models.Idiom
	if err := s.db.First(&idiom, idiomID).Error; err != nil {
		return nil, err
	}

	// 增加使用次数
	s.db.Model(&idiom).UpdateColumn("usage_count", gorm.Expr("usage_count + ?", 1))

	return &idiom, nil
}

// SearchIdioms 搜索成语
func (s *IdiomService) SearchIdioms(keyword string, page, pageSize int) ([]models.Idiom, int64, error) {
	var idioms []models.Idiom
	var total int64

	query := s.db.Model(&models.Idiom{}).Where("idiom LIKE ? OR meaning LIKE ?", "%"+keyword+"%", "%"+keyword+"%")

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("usage_count DESC").Find(&idioms).Error; err != nil {
		return nil, 0, err
	}

	return idioms, total, nil
}

// GetRandomIdioms 获取随机成语
func (s *IdiomService) GetRandomIdioms(count int, difficulty string) ([]models.Idiom, error) {
	var idioms []models.Idiom

	query := s.db.Model(&models.Idiom{})

	if difficulty != "" {
		query = query.Where("difficulty = ?", difficulty)
	}

	if err := query.Order("RAND()").Limit(count).Find(&idioms).Error; err != nil {
		return nil, err
	}

	return idioms, nil
}
