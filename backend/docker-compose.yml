version: '3.8'

services:
  idiom-maze-api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_HOST=************
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=minMIN123@
      - DB_DATABASE=idiom_maze
      - REDIS_HOST=************
      - REDIS_PORT=6379
      - REDIS_PASSWORD=root123456
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - idiom-maze-network

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=minMIN123@
      - MYSQL_DATABASE=idiom_maze
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    restart: unless-stopped
    networks:
      - idiom-maze-network

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass root123456
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - idiom-maze-network

volumes:
  mysql_data:
  redis_data:

networks:
  idiom-maze-network:
    driver: bridge
