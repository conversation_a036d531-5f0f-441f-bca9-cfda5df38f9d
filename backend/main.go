package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	
	"idiom-maze/config"
	"idiom-maze/database"
	"idiom-maze/middleware"
	"idiom-maze/routes"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.Close()

	// 初始化Redis
	redis, err := database.InitializeRedis(cfg.Redis)
	if err != nil {
		log.Fatal("Failed to initialize Redis:", err)
	}
	defer redis.Close()

	// 设置Gin模式
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORS())
	r.Use(middleware.RateLimiter())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "idiom-maze-api",
			"version": "1.0.0",
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 用户相关路由
		userRoutes := api.Group("/users")
		routes.SetupUserRoutes(userRoutes, db, redis)

		// 游戏相关路由
		gameRoutes := api.Group("/game")
		routes.SetupGameRoutes(gameRoutes, db, redis)

		// 成语相关路由
		idiomRoutes := api.Group("/idioms")
		routes.SetupIdiomRoutes(idiomRoutes, db, redis)

		// 排行榜相关路由
		leaderboardRoutes := api.Group("/leaderboard")
		routes.SetupLeaderboardRoutes(leaderboardRoutes, db, redis)

		// 管理后台路由（需要管理员权限）
		adminRoutes := api.Group("/admin")
		adminRoutes.Use(middleware.AdminAuth())
		routes.SetupAdminRoutes(adminRoutes, db, redis)
	}

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = cfg.Server.Port
	}

	log.Printf("Server starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
