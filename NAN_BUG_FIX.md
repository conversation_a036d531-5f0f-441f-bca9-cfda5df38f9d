# 🚨 NaN Bug 紧急修复

## 🎯 问题确认

### 错误现象
```
距离计算：rowDiff: NaN
不相邻，无法移动
无法移动到该位置
```

### 问题根源
**数据传递链条中某个环节出现了 `undefined` 或无效数据**

## 🔧 已实施全面修复

### 1. 增强数据验证
```javascript
// 检查输入参数是否有效
if (targetRow === undefined || targetCol === undefined || 
    isNaN(targetRow) || isNaN(targetCol)) {
  console.log('输入参数无效');
  return false;
}

// 检查玩家位置数据是否有效
if (currentRow === undefined || currentCol === undefined ||
    isNaN(currentRow) || isNaN(currentCol)) {
  console.log('玩家位置数据无效:', { currentRow, currentCol });
  return false;
}
```

### 2. 详细调试日志
```javascript
console.log('点击位置原始数据:', { 
  index, row, col, 
  indexType: typeof index,
  rowType: typeof row,
  colType: typeof col
});

console.log('canMoveTo 输入参数:', {
  targetRow, targetCol,
  targetRowType: typeof targetRow,
  targetColType: typeof targetCol,
  playerPosition
});
```

### 3. 安全的数据转换
```javascript
const targetR = Number(targetRow);
const targetC = Number(targetCol);
const currentR = Number(currentRow);
const currentC = Number(currentCol);
```

## 🧪 调试步骤

### 重新加载页面后：

#### 1. 查看游戏启动
控制台应显示：
```
=== generateMazeDefault 开始执行 ===
setData后的数据:
- mazeSize: 5
- playerPosition: {row: 0, col: 0}
游戏开始，迷宫数据: 25
```

#### 2. 点击玩家右边格子
应该看到详细日志：
```
点击位置原始数据: {
  index: "1",
  row: "0", 
  col: "1",
  indexType: "string",
  rowType: "string", 
  colType: "string"
}

转换后的数据: {
  targetRow: 0,
  targetCol: 1,
  targetRowType: "number",
  targetColType: "number",
  isNaN_row: false,
  isNaN_col: false
}

canMoveTo 输入参数: {
  targetRow: 0,
  targetCol: 1,
  playerPosition: {row: 0, col: 0}
}

移动检查: {
  current: {row: 0, col: 0},
  target: {row: 0, col: 1}
}

距离计算: {rowDiff: 0, colDiff: 1}
```

## 🎯 可能的问题场景

### 场景A: 模板数据传递问题
**现象**: `row: undefined` 或 `col: undefined`
**原因**: WXML中的`data-row`或`data-col`计算错误
**解决**: 检查模板中的`Math.floor(index / mazeSize)`

### 场景B: 玩家位置未初始化
**现象**: `playerPosition: undefined` 或 `{}`
**原因**: `generateMazeDefault`中没有正确设置
**解决**: 检查`setData`是否正确执行

### 场景C: 数据类型混乱
**现象**: 某些值是字符串，某些是数字
**原因**: 不同地方的数据处理不一致
**解决**: 统一使用`Number()`转换

## 🚀 测试指令

**请立即执行**：

1. **重新加载游戏页面**
2. **打开控制台**
3. **点击玩家右边的白色格子**
4. **复制完整的控制台输出**

### 重点关注：
- `点击位置原始数据` - 是否有 `undefined`？
- `转换后的数据` - `isNaN_row` 和 `isNaN_col` 是否为 `false`？
- `playerPosition` - 是否正确显示 `{row: 0, col: 0}`？
- `距离计算` - 是否还是 `NaN`？

## 💡 备用解决方案

### 如果问题仍然存在：

#### 方案1: 硬编码测试
```javascript
// 临时允许移动到 (0,1) 位置
canMoveTo(targetRow, targetCol) {
  if (targetRow === 0 && targetCol === 1) {
    console.log('硬编码允许移动到(0,1)');
    return true;
  }
  // 原有逻辑...
}
```

#### 方案2: 简化移动检查
```javascript
// 临时移除相邻检查
canMoveTo(targetRow, targetCol) {
  const targetIndex = targetRow * this.data.mazeSize + targetCol;
  const targetCell = this.data.mazeData[targetIndex];
  return targetCell && targetCell.type === 'path';
}
```

## 🎉 预期结果

修复后应该看到：
- ✅ **所有数据类型正确** - 没有 `undefined` 或 `NaN`
- ✅ **距离计算正确** - `{rowDiff: 0, colDiff: 1}`
- ✅ **"可以移动到该位置"** - 成功通过检查
- ✅ **玩家成功移动** - 金色标记更新位置

**我们马上就能解决这个 NaN 问题！** 🚀

请告诉我新的控制台输出，特别是那些调试信息的具体内容。
