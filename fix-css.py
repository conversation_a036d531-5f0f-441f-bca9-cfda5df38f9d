#!/usr/bin/env python3
# 修复CSS变量的Python脚本

import os
import re

# CSS变量映射
css_vars = {
    'var(--primary-color)': '#FFD700',
    'var(--secondary-color)': '#1E3A8A',
    'var(--background-color)': '#FEFEFE',
    'var(--text-color)': '#2D3748',
    'var(--success-color)': '#10B981',
    'var(--warning-color)': '#F59E0B',
    'var(--error-color)': '#EF4444',
    'var(--font-size-large)': '18px',
    'var(--font-size-medium)': '16px',
    'var(--font-size-small)': '14px',
    'var(--font-size-mini)': '12px',
    'var(--spacing-large)': '20px',
    'var(--spacing-medium)': '16px',
    'var(--spacing-small)': '12px',
    'var(--spacing-mini)': '8px',
    'var(--border-radius)': '8px',
    'var(--button-height)': '44px'
}

# 需要处理的文件
files = [
    'pages/index/index.wxss',
    'pages/game/game.wxss',
    'pages/test/test.wxss'
]

def replace_css_vars(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换所有CSS变量
        for css_var, value in css_vars.items():
            content = content.replace(css_var, value)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f'✅ 已修复: {file_path}')
        else:
            print(f'⏭️  无需修复: {file_path}')
            
    except Exception as e:
        print(f'❌ 处理失败: {file_path} - {str(e)}')

# 处理所有文件
for file in files:
    if os.path.exists(file):
        replace_css_vars(file)
    else:
        print(f'⚠️  文件不存在: {file}')

print('🎉 CSS变量修复完成！')
