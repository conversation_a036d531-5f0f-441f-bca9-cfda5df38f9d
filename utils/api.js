// utils/api.js - API请求工具

const config = require('./config.js');

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (options.loading !== false) {
      wx.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      });
    }

    // 构建完整URL
    const url = options.url.startsWith('http') 
      ? options.url 
      : config.API_BASE_URL + options.url;

    // 发起请求
    wx.request({
      url: url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        // 添加认证token（如果有）
        ...(options.token && { 'Authorization': `Bearer ${options.token}` }),
        ...options.header
      },
      timeout: config.API_TIMEOUT,
      success: (res) => {
        // 隐藏加载提示
        if (options.loading !== false) {
          wx.hideLoading();
        }

        // 处理响应
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res.data.data);
          } else {
            // 业务错误
            const error = new Error(res.data.message || '请求失败');
            error.code = res.data.code;
            reject(error);
          }
        } else {
          // HTTP错误
          reject(new Error(`HTTP ${res.statusCode}: ${res.data.message || '网络错误'}`));
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (options.loading !== false) {
          wx.hideLoading();
        }

        // 网络错误处理
        let errorMessage = '网络连接失败';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
          } else if (err.errMsg.includes('fail')) {
            errorMessage = '网络请求失败，请稍后重试';
          }
        }
        
        reject(new Error(errorMessage));
      }
    });
  });
};

// API方法封装
const api = {
  // GET请求
  get: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'GET',
      data,
      ...options
    });
  },

  // POST请求
  post: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    });
  },

  // PUT请求
  put: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  },

  // DELETE请求
  delete: (url, data = {}, options = {}) => {
    return request({
      url,
      method: 'DELETE',
      data,
      ...options
    });
  }
};

// 具体业务API
const gameApi = {
  // 获取关卡列表
  getLevels: (page = 1, pageSize = 20, difficulty = '') => {
    return api.get('/game/levels', { page, page_size: pageSize, difficulty });
  },

  // 获取单个关卡
  getLevel: (levelId) => {
    return api.get(`/game/levels/${levelId}`);
  },

  // 开始游戏
  startGame: (levelId, token) => {
    return api.post('/game/start', { level_id: levelId }, { token });
  },

  // 保存游戏进度
  saveProgress: (data, token) => {
    return api.post('/game/save-progress', data, { token, loading: false });
  },

  // 获取游戏进度
  getProgress: (userId, levelId = '') => {
    return api.get(`/game/progress/${userId}`, { level_id: levelId });
  },

  // 完成游戏
  completeGame: (data, token) => {
    return api.post('/game/complete', data, { token });
  },

  // 生成迷宫
  generateMaze: (size = 7, difficulty = 1) => {
    return api.get('/game/maze/generate', { size, difficulty });
  }
};

const idiomApi = {
  // 获取成语列表
  getIdioms: (page = 1, pageSize = 20, difficulty = '', category = '') => {
    return api.get('/idioms/', { page, page_size: pageSize, difficulty, category });
  },

  // 获取单个成语
  getIdiom: (idiomId) => {
    return api.get(`/idioms/${idiomId}`);
  },

  // 搜索成语
  searchIdioms: (keyword, page = 1, pageSize = 20) => {
    return api.get('/idioms/search', { keyword, page, page_size: pageSize });
  },

  // 获取随机成语
  getRandomIdioms: (count = 10, difficulty = '') => {
    return api.get('/idioms/random', { count, difficulty });
  }
};

const userApi = {
  // 用户登录
  login: (code, userInfo) => {
    return api.post('/users/login', {
      code,
      nickname: userInfo.nickName,
      avatar: userInfo.avatarUrl,
      gender: userInfo.gender,
      city: userInfo.city,
      province: userInfo.province,
      country: userInfo.country
    });
  },

  // 获取用户资料
  getUserProfile: (userId) => {
    return api.get(`/users/profile/${userId}`);
  },

  // 更新用户资料
  updateUserProfile: (data, token) => {
    return api.put('/users/profile', data, { token });
  },

  // 获取用户统计
  getUserStats: (userId) => {
    return api.get(`/users/stats/${userId}`);
  }
};

const leaderboardApi = {
  // 获取总分排行榜
  getTotalScoreLeaderboard: (page = 1, pageSize = 50) => {
    return api.get('/leaderboard/total', { page, page_size: pageSize });
  },

  // 获取关卡排行榜
  getLevelLeaderboard: (levelId, page = 1, pageSize = 50) => {
    return api.get(`/leaderboard/level/${levelId}`, { page, page_size: pageSize });
  },

  // 获取速度排行榜
  getSpeedLeaderboard: (page = 1, pageSize = 50) => {
    return api.get('/leaderboard/speed', { page, page_size: pageSize });
  },

  // 获取用户排名
  getUserRanking: (userId) => {
    return api.get(`/leaderboard/user/${userId}`);
  }
};

module.exports = {
  api,
  gameApi,
  idiomApi,
  userApi,
  leaderboardApi
};
