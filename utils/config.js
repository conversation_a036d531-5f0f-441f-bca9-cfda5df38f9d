// utils/config.js - 小程序配置文件

// API配置
const API_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'http://localhost:8080/api/v1',
    timeout: 10000
  },
  
  // 生产环境
  production: {
    baseURL: 'https://your-domain.com/api/v1',
    timeout: 10000
  }
};

// 当前环境
const ENV = 'development'; // 发布时改为 'production'

// 导出配置
module.exports = {
  API_BASE_URL: API_CONFIG[ENV].baseURL,
  API_TIMEOUT: API_CONFIG[ENV].timeout,
  
  // 游戏配置
  GAME_CONFIG: {
    MAX_HINTS_PER_LEVEL: 3,
    HINT_SCORE_PENALTY: 10,
    TIME_BONUS_FACTOR: 1.5,
    STAR_THRESHOLDS: {
      1: 60,
      2: 80,
      3: 95
    }
  },
  
  // 存储键名
  STORAGE_KEYS: {
    USER_INFO: 'userInfo',
    GAME_DATA: 'gameData',
    SETTINGS: 'settings',
    GAME_PROGRESS: 'gameProgress'
  }
};
