# 🎮 成语迷宫小程序

一个结合传统文化与现代游戏设计的微信小程序，通过迷宫探索的方式学习成语知识。

## 📱 项目概述

### 🎯 核心功能
- **迷宫探索**: 在迷宫中寻找成语字符，完成成语拼接
- **成语学习**: 丰富的成语库，包含释义、例句、出处等详细信息
- **关卡挑战**: 多个难度等级，循序渐进的学习体验
- **排行榜系统**: 与好友比拼分数和通关速度
- **成就系统**: 解锁各种成就，增加游戏趣味性

### 🏗️ 技术架构
- **前端**: 微信小程序原生开发
- **后端**: Go + Gin框架
- **数据库**: MySQL + Redis
- **部署**: Docker容器化部署

## 🚀 快速开始

### 📋 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 7.0+
- 微信开发者工具

### 🔧 后端部署

1. **克隆项目**
```bash
git clone <repository-url>
cd chengyumigong/backend
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接信息
```

3. **安装依赖**
```bash
go mod tidy
```

4. **初始化数据库**
```bash
# 连接到MySQL，执行database/schema.sql
mysql -h 47.92.81.180 -u root -p idiom_maze < database/schema.sql
```

5. **启动服务**
```bash
# 开发环境
go run main.go

# 或使用Docker
docker-compose up -d
```

### 📱 小程序配置

1. **导入项目**
   - 使用微信开发者工具导入小程序项目
   - 配置AppID和服务器域名

2. **配置API地址**
   - 修改小程序中的API基础地址
   - 配置合法域名

3. **编译运行**
   - 在微信开发者工具中编译运行
   - 真机调试测试功能

## 📊 数据库设计

### 核心表结构
- `users`: 用户信息表
- `idioms`: 成语数据表
- `levels`: 关卡配置表
- `user_game_records`: 游戏记录表
- `leaderboards`: 排行榜表
- `user_achievements`: 用户成就表

## 🔌 API接口

### 用户相关
- `POST /api/v1/users/login` - 用户登录
- `GET /api/v1/users/profile/:id` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料

### 游戏相关
- `GET /api/v1/game/levels` - 获取关卡列表
- `POST /api/v1/game/start` - 开始游戏
- `POST /api/v1/game/save-progress` - 保存进度
- `POST /api/v1/game/complete` - 完成游戏

### 成语相关
- `GET /api/v1/idioms` - 获取成语列表
- `GET /api/v1/idioms/search` - 搜索成语
- `GET /api/v1/idioms/random` - 获取随机成语

### 排行榜相关
- `GET /api/v1/leaderboard/total` - 总分排行榜
- `GET /api/v1/leaderboard/level/:levelId` - 关卡排行榜
- `GET /api/v1/leaderboard/speed` - 速度排行榜

## 🎨 设计特色

### UI设计
- **中国风主题**: 金黄色主色调，传统文化元素
- **响应式布局**: 适配不同尺寸的移动设备
- **流畅动画**: 丰富的交互动画效果

### 游戏机制
- **难度分级**: 根据用户水平调整难度
- **智能提示**: 适时提供学习帮助
- **星级评价**: 多维度评价游戏表现

## 📈 性能优化

### 前端优化
- 组件化开发，提高代码复用性
- 图片资源压缩和懒加载
- 本地存储优化用户体验

### 后端优化
- Redis缓存热点数据
- 数据库索引优化
- API接口限流保护

## 🔒 安全措施

- JWT token身份验证
- API接口限流
- 数据输入验证
- SQL注入防护

## 📝 开发日志

### v1.0.0 (2024-01-XX)
- ✅ 完成基础架构搭建
- ✅ 实现核心游戏功能
- ✅ 完成数据库设计
- ✅ 实现API接口
- ✅ 完成小程序前端

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/idiom-maze](https://github.com/yourusername/idiom-maze)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
