// pages/index/index.js
const app = getApp();

Page({
  data: {
    gameData: {},
    hasProgress: false,
    showLevelModal: false,
    showTutorialModal: false,
    levels: [],
    mazePattern: [
      'wall', 'path', 'wall', 'path', 'wall',
      'path', 'path', 'path', 'path', 'path',
      'wall', 'path', 'wall', 'path', 'wall',
      'path', 'path', 'path', 'path', 'path',
      'wall', 'path', 'wall', 'path', 'wall'
    ]
  },

  onLoad() {
    console.log('首页加载');
    this.initPageData();
  },

  onShow() {
    // 每次显示页面时更新数据
    this.setData({
      gameData: app.globalData.gameData
    });
    this.checkGameProgress();
  },

  // 初始化页面数据
  initPageData() {
    this.setData({
      gameData: app.globalData.gameData
    });
    this.generateLevels();
    this.checkGameProgress();
  },

  // 生成关卡数据
  generateLevels() {
    const levels = [];
    for (let i = 1; i <= 50; i++) {
      levels.push({
        level: i,
        stars: i <= app.globalData.gameData.currentLevel ? Math.floor(Math.random() * 4) : 0,
        unlocked: i <= app.globalData.gameData.unlockedLevels
      });
    }
    this.setData({ levels });
  },

  // 检查游戏进度
  checkGameProgress() {
    const hasProgress = app.globalData.gameData.currentLevel > 1;
    this.setData({ hasProgress });
  },

  // 开始游戏
  startGame() {
    wx.vibrateShort();
    wx.navigateTo({
      url: `/pages/game/game?level=${this.data.gameData.currentLevel}`
    });
  },

  // 继续游戏
  continueGame() {
    wx.vibrateShort();
    wx.navigateTo({
      url: `/pages/game/game?level=${this.data.gameData.currentLevel}&continue=true`
    });
  },

  // 显示关卡选择
  showLevelSelect() {
    this.setData({ showLevelModal: true });
  },

  // 隐藏关卡选择
  hideLevelSelect() {
    this.setData({ showLevelModal: false });
  },

  // 选择关卡
  selectLevel(e) {
    const level = e.currentTarget.dataset.level;
    if (level <= this.data.gameData.unlockedLevels) {
      wx.vibrateShort();
      wx.navigateTo({
        url: `/pages/game/game?level=${level}`
      });
    } else {
      wx.showToast({
        title: '关卡未解锁',
        icon: 'none'
      });
    }
  },

  // 显示排行榜
  showRanking() {
    wx.showToast({
      title: '排行榜功能开发中',
      icon: 'none'
    });
  },

  // 显示教程
  showTutorial() {
    this.setData({ showTutorialModal: true });
  },

  // 隐藏教程
  hideTutorial() {
    this.setData({ showTutorialModal: false });
  },

  // 显示成就
  showAchievements() {
    wx.showToast({
      title: '成就系统开发中',
      icon: 'none'
    });
  },

  // 阻止弹窗关闭
  preventClose() {
    // 空函数，阻止事件冒泡
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '成语迷宫 - 传统文化益智游戏',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '成语迷宫 - 在游戏中学习传统文化',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});
