/* pages/index/index.wxss */

.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  padding: var(--spacing-large);
  display: flex;
  flex-direction: column;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: var(--spacing-large);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title-wrapper {
  margin-bottom: var(--spacing-large);
}

.main-title {
  display: block;
  font-size: 36px;
  font-weight: bold;
  color: #2D3748;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--spacing-small);
}

.subtitle {
  font-size: var(--font-size-medium);
  color: #4A5568;
  opacity: 0.8;
}

/* 装饰性迷宫图标 */
.maze-icon {
  margin: var(--spacing-large) 0;
}

.maze-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2px;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}

.maze-cell {
  border-radius: 2px;
  transition: all 0.3s ease;
}

.maze-cell[data-type="wall"] {
  background-color: #2D3748;
}

.maze-cell[data-type="path"] {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 游戏统计 */
.stats-section {
  display: flex;
  justify-content: space-around;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius);
  padding: var(--spacing-medium);
  margin-bottom: var(--spacing-large);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-mini);
}

.stat-label {
  font-size: var(--font-size-small);
  color: #666;
}

/* 主要操作按钮 */
.action-section {
  margin-bottom: var(--spacing-large);
}

.start-btn {
  width: 100%;
  height: 60px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: var(--spacing-medium);
  position: relative;
  overflow: hidden;
}

.btn-text {
  margin-right: var(--spacing-small);
}

.btn-icon {
  font-size: 24px;
}

.secondary-buttons {
  display: flex;
  gap: var(--spacing-small);
}

.secondary-buttons .btn {
  flex: 1;
  height: 40px;
  font-size: var(--font-size-small);
}

/* 快速入口 */
.quick-access {
  display: flex;
  justify-content: space-around;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius);
  padding: var(--spacing-medium);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-small);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  flex: 1;
}

.access-item:active {
  background-color: rgba(255, 215, 0, 0.2);
  transform: scale(0.95);
}

.access-icon {
  font-size: 24px;
  margin-bottom: var(--spacing-mini);
}

.access-text {
  font-size: var(--font-size-small);
  color: #666;
  text-align: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.level-modal,
.tutorial-modal {
  background-color: white;
  border-radius: var(--border-radius);
  width: 80%;
  max-height: 70%;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-medium);
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--text-color);
}

.modal-close {
  font-size: 20px;
  color: #999;
  padding: var(--spacing-mini);
}

/* 关卡网格 */
.level-grid {
  padding: var(--spacing-medium);
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--spacing-small);
  max-height: 400px;
}

.level-item {
  aspect-ratio: 1;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
}

.level-item.unlocked {
  background-color: var(--primary-color);
  color: #333;
}

.level-item.locked {
  background-color: #f5f5f5;
  color: #999;
}

.level-item:active {
  transform: scale(0.95);
}

.level-number {
  font-weight: bold;
  font-size: var(--font-size-small);
}

.level-stars {
  font-size: 10px;
  margin-top: 2px;
}

.level-lock {
  font-size: 16px;
}

/* 教程内容 */
.tutorial-content {
  padding: var(--spacing-medium);
  max-height: 400px;
}

.tutorial-step {
  margin-bottom: var(--spacing-medium);
  padding: var(--spacing-small);
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
}

.step-title {
  display: block;
  font-weight: bold;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-mini);
}

.step-desc {
  font-size: var(--font-size-small);
  color: #666;
  line-height: 1.6;
}
