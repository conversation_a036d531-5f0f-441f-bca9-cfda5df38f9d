/* pages/index/index.wxss */

.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title-wrapper {
  margin-bottom: 20px;
}

.main-title {
  display: block;
  font-size: 36px;
  font-weight: bold;
  color: #2D3748;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
}

.subtitle {
  font-size: 16px;
  color: #4A5568;
  opacity: 0.8;
}

/* 装饰性迷宫图标 */
.maze-icon {
  margin: 20px 0;
}

.maze-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2px;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}

.maze-cell {
  border-radius: 2px;
  transition: all 0.3s ease;
}

.maze-cell[data-type="wall"] {
  background-color: #2D3748;
}

.maze-cell[data-type="path"] {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 游戏统计 */
.stats-section {
  display: flex;
  justify-content: space-around;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 主要操作按钮 */
.action-section {
  margin-bottom: 20px;
}

.start-btn {
  width: 100%;
  height: 60px;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.btn-text {
  margin-right: 12px;
}

.btn-icon {
  font-size: 24px;
}

.secondary-buttons {
  display: flex;
  gap: 12px;
}

.secondary-buttons .btn {
  flex: 1;
  height: 40px;
  font-size: 14px;
}

/* 快速入口 */
.quick-access {
  display: flex;
  justify-content: space-around;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  flex: 1;
}

.access-item:active {
  background-color: rgba(255, 215, 0, 0.2);
  transform: scale(0.95);
}

.access-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.access-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.level-modal,
.tutorial-modal {
  background-color: white;
  border-radius: 8px;
  width: 85%;
  max-width: 400px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideUp 0.3s ease-out;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #2D3748;
}

.modal-close {
  font-size: 20px;
  color: #999;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.modal-close:hover {
  background-color: #f5f5f5;
  color: #666;
}

.modal-close:active {
  transform: scale(0.9);
}

/* 关卡网格 */
.level-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  max-height: 50vh;
  overflow-y: auto;
}

.level-item {
  aspect-ratio: 1;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.level-item.unlocked {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  border-color: #FFD700;
}

.level-item.unlocked:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.level-item.locked {
  background-color: #f5f5f5;
  color: #999;
  border-color: #e0e0e0;
}

.level-item:active {
  transform: scale(0.95);
}

.level-number {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}

.level-stars {
  font-size: 12px;
  margin-top: 2px;
  display: flex;
  gap: 1px;
}

.level-lock {
  font-size: 18px;
  opacity: 0.6;
}

/* 教程内容 */
.tutorial-content {
  padding: 16px;
  max-height: 50vh;
  overflow-y: auto;
  flex: 1;
}

.tutorial-step {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #FFD700;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.step-title {
  display: block;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 8px;
  font-size: 16px;
}

.step-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 动画定义 */
@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 滚动条样式优化 */
.level-grid::-webkit-scrollbar,
.tutorial-content::-webkit-scrollbar {
  width: 4px;
}

.level-grid::-webkit-scrollbar-track,
.tutorial-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.level-grid::-webkit-scrollbar-thumb,
.tutorial-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.level-grid::-webkit-scrollbar-thumb:hover,
.tutorial-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .level-modal,
  .tutorial-modal {
    width: 90%;
    max-height: 85vh;
  }

  .level-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .level-number {
    font-size: 14px;
  }

  .level-stars {
    font-size: 10px;
  }
}

/* 大屏优化 */
@media (min-width: 768px) {
  .level-modal,
  .tutorial-modal {
    width: 60%;
    max-width: 500px;
  }

  .level-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
