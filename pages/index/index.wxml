<!-- pages/index/index.wxml -->
<view class="index-container">
  <!-- 标题区域 -->
  <view class="header-section fade-in">
    <view class="title-wrapper">
      <text class="main-title">成语迷宫</text>
      <text class="subtitle">传统文化 · 益智游戏</text>
    </view>
    
    <!-- 装饰性迷宫图标 -->
    <view class="maze-icon">
      <view class="maze-grid">
        <view class="maze-cell" wx:for="{{mazePattern}}" wx:key="index" 
              data-type="{{item}}"></view>
      </view>
    </view>
  </view>

  <!-- 游戏统计 -->
  <view class="stats-section slide-up">
    <view class="stat-item">
      <text class="stat-number">{{gameData.currentLevel}}</text>
      <text class="stat-label">当前关卡</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{gameData.totalScore}}</text>
      <text class="stat-label">总分数</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{gameData.unlockedLevels}}</text>
      <text class="stat-label">已解锁</text>
    </view>
  </view>

  <!-- 主要操作按钮 -->
  <view class="action-section">
    <button class="btn btn-primary start-btn bounce" bindtap="startGame">
      <text class="btn-text">开始游戏</text>
      <text class="btn-icon">🎮</text>
    </button>
    
    <view class="secondary-buttons">
      <button class="btn btn-outline" bindtap="showLevelSelect">
        选择关卡
      </button>
      <button class="btn btn-outline" bindtap="showRanking">
        排行榜
      </button>
    </view>
  </view>

  <!-- 快速入口 -->
  <view class="quick-access">
    <view class="access-item" bindtap="continueGame" wx:if="{{hasProgress}}">
      <text class="access-icon">📖</text>
      <text class="access-text">继续游戏</text>
    </view>
    <view class="access-item" bindtap="showTutorial">
      <text class="access-icon">❓</text>
      <text class="access-text">游戏教程</text>
    </view>
    <view class="access-item" bindtap="showAchievements">
      <text class="access-icon">🏆</text>
      <text class="access-text">成就系统</text>
    </view>
  </view>

  <!-- 关卡选择弹窗 -->
  <view class="modal-overlay" wx:if="{{showLevelModal}}" bindtap="hideLevelSelect">
    <view class="level-modal" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">选择关卡</text>
        <text class="modal-close" bindtap="hideLevelSelect">✕</text>
      </view>
      <scroll-view class="level-grid" scroll-y>
        <view class="level-item {{index < gameData.unlockedLevels ? 'unlocked' : 'locked'}}"
              wx:for="{{levels}}" wx:key="index"
              bindtap="selectLevel" data-level="{{index + 1}}">
          <text class="level-number">{{index + 1}}</text>
          <text class="level-stars" wx:if="{{item.stars > 0}}">
            <text wx:for="{{item.stars}}" wx:key="star">⭐</text>
          </text>
          <text class="level-lock" wx:if="{{index >= gameData.unlockedLevels}}">🔒</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 教程弹窗 -->
  <view class="modal-overlay" wx:if="{{showTutorialModal}}" bindtap="hideTutorial">
    <view class="tutorial-modal" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">游戏教程</text>
        <text class="modal-close" bindtap="hideTutorial">✕</text>
      </view>
      <scroll-view class="tutorial-content" scroll-y>
        <view class="tutorial-step">
          <text class="step-title">🎯 游戏目标</text>
          <text class="step-desc">在迷宫中找到正确的成语路径，完成成语填空挑战。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">🕹️ 操作方法</text>
          <text class="step-desc">点击迷宫格子移动，找到成语后选择正确答案。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">⭐ 评分系统</text>
          <text class="step-desc">根据完成时间和提示使用次数获得1-3星评价。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">💡 提示功能</text>
          <text class="step-desc">遇到困难可以使用提示，但会影响最终评分。</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
