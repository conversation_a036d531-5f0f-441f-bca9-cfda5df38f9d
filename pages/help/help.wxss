/* pages/help/help.wxss */

.help-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 32rpx;
}

/* 页面标题 */
.help-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.help-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 16rpx;
}

.help-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6B7280;
}

/* 章节样式 */
.help-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #E5E7EB;
}

.section-content {
  line-height: 1.6;
}

.content-text {
  font-size: 32rpx;
  color: #4B5563;
  line-height: 1.8;
}

/* 操作项目 */
.operation-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #F9FAFB;
  border-radius: 16rpx;
}

.operation-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.operation-desc {
  flex: 1;
}

.operation-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.operation-detail {
  display: block;
  font-size: 28rpx;
  color: #6B7280;
  line-height: 1.6;
}

/* 游戏元素网格 */
.element-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.element-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #F9FAFB;
  border-radius: 16rpx;
}

.element-demo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  border: 2rpx solid #E5E7EB;
}

.player-demo {
  background-color: #FFD700;
  border-color: #FFA500;
}

.target-demo {
  background-color: #48bb78;
  border-color: #38a169;
}

.character-demo {
  background-color: #fef5e7;
  border-color: #d69e2e;
  color: #744210;
}

.path-demo {
  background-color: #ffffff;
  border-color: #e2e8f0;
}

.wall-demo {
  background-color: #2D3748;
  border-color: #4A5568;
}

.visited-demo {
  background-color: #e6fffa;
  border-color: #38b2ac;
}

.element-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.element-desc {
  font-size: 24rpx;
  color: #6B7280;
}

/* 规则项目 */
.rule-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #F0FDF4;
  border-radius: 12rpx;
  border-left: 8rpx solid #10B981;
}

.rule-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #10B981;
}

.rule-text {
  font-size: 30rpx;
  color: #1F2937;
  line-height: 1.6;
}

/* 流程步骤 */
.flow-steps {
  position: relative;
}

.flow-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
}

.flow-step:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 40rpx;
  top: 80rpx;
  width: 4rpx;
  height: 60rpx;
  background: #E5E7EB;
}

.step-number {
  width: 80rpx;
  height: 80rpx;
  background: #3B82F6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  padding-top: 8rpx;
}

.step-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.step-desc {
  display: block;
  font-size: 28rpx;
  color: #6B7280;
  line-height: 1.6;
}

/* 技巧项目 */
.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #FEF3C7;
  border-radius: 12rpx;
  border-left: 8rpx solid #F59E0B;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.tip-text {
  font-size: 30rpx;
  color: #1F2937;
  line-height: 1.6;
}

/* 评分项目 */
.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #E5E7EB;
}

.score-item:last-child {
  border-bottom: none;
}

.score-label {
  font-size: 30rpx;
  font-weight: bold;
  color: #1F2937;
}

.score-value {
  font-size: 28rpx;
  color: #6B7280;
}

/* 控制按钮网格 */
.control-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #F3F4F6;
  border-radius: 16rpx;
}

.control-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.control-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.control-desc {
  font-size: 24rpx;
  color: #6B7280;
  text-align: center;
}

/* 页脚按钮 */
.help-footer {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
  padding: 32rpx;
}

.back-btn, .home-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.back-btn {
  background: #3B82F6;
  color: white;
}

.back-btn:active {
  background: #2563EB;
}

.home-btn {
  background: #6B7280;
  color: white;
}

.home-btn:active {
  background: #4B5563;
}
