<!--pages/help/help.wxml-->
<view class="help-container">
  <!-- 页面标题 -->
  <view class="help-header">
    <text class="help-title">🎮 成语迷宫 - 游戏玩法</text>
    <text class="help-subtitle">学成语，玩迷宫，寓教于乐！</text>
  </view>

  <!-- 游戏目标 -->
  <view class="help-section">
    <view class="section-title">🎯 游戏目标</view>
    <view class="section-content">
      <text class="content-text">在迷宫中移动玩家，收集散落的成语字符，组成完整的成语，最后到达终点完成关卡！</text>
    </view>
  </view>

  <!-- 基本操作 -->
  <view class="help-section">
    <view class="section-title">🕹️ 基本操作</view>
    <view class="section-content">
      <view class="operation-item">
        <text class="operation-icon">👆</text>
        <view class="operation-desc">
          <text class="operation-title">移动玩家</text>
          <text class="operation-detail">点击玩家相邻的白色格子，玩家会移动到该位置</text>
        </view>
      </view>
      
      <view class="operation-item">
        <text class="operation-icon">📝</text>
        <view class="operation-desc">
          <text class="operation-title">收集字符</text>
          <text class="operation-detail">移动到有成语字符的格子时，字符会自动被收集</text>
        </view>
      </view>
      
      <view class="operation-item">
        <text class="operation-icon">🎯</text>
        <view class="operation-desc">
          <text class="operation-title">到达终点</text>
          <text class="operation-detail">收集完所有字符后，移动到绿色终点格子完成关卡</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏元素 -->
  <view class="help-section">
    <view class="section-title">🎨 游戏元素</view>
    <view class="section-content">
      <view class="element-grid">
        <view class="element-item">
          <view class="element-demo player-demo"></view>
          <text class="element-name">玩家位置</text>
          <text class="element-desc">金色背景</text>
        </view>
        
        <view class="element-item">
          <view class="element-demo target-demo"></view>
          <text class="element-name">目标终点</text>
          <text class="element-desc">绿色背景</text>
        </view>
        
        <view class="element-item">
          <view class="element-demo character-demo">画</view>
          <text class="element-name">成语字符</text>
          <text class="element-desc">浅黄背景</text>
        </view>
        
        <view class="element-item">
          <view class="element-demo path-demo"></view>
          <text class="element-name">通路</text>
          <text class="element-desc">白色背景</text>
        </view>
        
        <view class="element-item">
          <view class="element-demo wall-demo"></view>
          <text class="element-name">墙壁</text>
          <text class="element-desc">灰色背景</text>
        </view>
        
        <view class="element-item">
          <view class="element-demo visited-demo"></view>
          <text class="element-name">已访问</text>
          <text class="element-desc">浅绿背景</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 移动规则 -->
  <view class="help-section">
    <view class="section-title">📏 移动规则</view>
    <view class="section-content">
      <view class="rule-item">
        <text class="rule-icon">✅</text>
        <text class="rule-text">只能移动到相邻的格子（上下左右，不能斜移）</text>
      </view>
      <view class="rule-item">
        <text class="rule-icon">✅</text>
        <text class="rule-text">只能在白色通路上移动，不能穿过灰色墙壁</text>
      </view>
      <view class="rule-item">
        <text class="rule-icon">✅</text>
        <text class="rule-text">一次只能移动一格，需要规划好路径</text>
      </view>
      <view class="rule-item">
        <text class="rule-icon">✅</text>
        <text class="rule-text">可以重复经过已访问的格子</text>
      </view>
    </view>
  </view>

  <!-- 游戏流程 -->
  <view class="help-section">
    <view class="section-title">🎮 游戏流程</view>
    <view class="section-content">
      <view class="flow-steps">
        <view class="flow-step">
          <view class="step-number">1</view>
          <view class="step-content">
            <text class="step-title">开始游戏</text>
            <text class="step-desc">进入游戏页面，等待迷宫加载完成</text>
          </view>
        </view>
        
        <view class="flow-step">
          <view class="step-number">2</view>
          <view class="step-content">
            <text class="step-title">移动探索</text>
            <text class="step-desc">点击相邻格子移动玩家，探索迷宫</text>
          </view>
        </view>
        
        <view class="flow-step">
          <view class="step-number">3</view>
          <view class="step-content">
            <text class="step-title">收集字符</text>
            <text class="step-desc">移动到成语字符格子，自动收集字符</text>
          </view>
        </view>
        
        <view class="flow-step">
          <view class="step-number">4</view>
          <view class="step-content">
            <text class="step-title">组成成语</text>
            <text class="step-desc">收集完整的成语字符，如"画蛇添足"</text>
          </view>
        </view>
        
        <view class="flow-step">
          <view class="step-number">5</view>
          <view class="step-content">
            <text class="step-title">到达终点</text>
            <text class="step-desc">移动到绿色终点格子，完成关卡</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏技巧 -->
  <view class="help-section">
    <view class="section-title">💡 游戏技巧</view>
    <view class="section-content">
      <view class="tip-item">
        <text class="tip-icon">🗺️</text>
        <text class="tip-text">先观察整个迷宫，规划最优路径再开始移动</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🎯</text>
        <text class="tip-text">优先收集距离较远的字符，避免走重复路径</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">⏰</text>
        <text class="tip-text">合理利用提示功能，但要注意会扣除分数</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🚫</text>
        <text class="tip-text">注意避开死胡同，确保能够返回主路径</text>
      </view>
    </view>
  </view>

  <!-- 评分系统 -->
  <view class="help-section">
    <view class="section-title">🏆 评分系统</view>
    <view class="section-content">
      <view class="score-item">
        <text class="score-label">基础分数：</text>
        <text class="score-value">完成关卡获得基础分数</text>
      </view>
      <view class="score-item">
        <text class="score-label">时间奖励：</text>
        <text class="score-value">用时越短，奖励分数越多</text>
      </view>
      <view class="score-item">
        <text class="score-label">提示惩罚：</text>
        <text class="score-value">使用提示会扣除10分</text>
      </view>
      <view class="score-item">
        <text class="score-label">星级评定：</text>
        <text class="score-value">根据总分获得1-3星评级</text>
      </view>
    </view>
  </view>

  <!-- 控制按钮 -->
  <view class="help-section">
    <view class="section-title">🎛️ 控制按钮</view>
    <view class="section-content">
      <view class="control-grid">
        <view class="control-item">
          <text class="control-icon">💡</text>
          <text class="control-name">提示</text>
          <text class="control-desc">获得游戏提示</text>
        </view>
        <view class="control-item">
          <text class="control-icon">🔄</text>
          <text class="control-name">重置</text>
          <text class="control-desc">重新开始关卡</text>
        </view>
        <view class="control-item">
          <text class="control-icon">⏸️</text>
          <text class="control-name">暂停</text>
          <text class="control-desc">暂停游戏计时</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="help-footer">
    <button class="back-btn" bindtap="goBack">返回游戏</button>
    <button class="home-btn" bindtap="goHome">返回首页</button>
  </view>
</view>
