/* pages/game/game.wxss */

.game-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}

/* 游戏头部 */
.game-header {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.level-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 16rpx;
}

.stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 32rpx;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.star.active {
  opacity: 1;
}

.game-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-icon {
  font-size: 36rpx;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #2D3748;
}

/* 进度条 */
.progress-container {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.progress-bar {
  flex: 1;
  height: 16rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 24rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #10B981);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  font-weight: bold;
  color: #1E3A8A;
  min-width: 40px;
}

/* 迷宫容器 */
.maze-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 20rpx;
}

.maze-grid {
  display: grid;
  gap: 4rpx;
  background-color: #2D3748;
  border-radius: 24rpx;
  padding: 16rpx;
  width: 680rpx;
  height: 680rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
}

.maze-cell {
  aspect-ratio: 1;
  position: relative;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  min-height: 80rpx;
  background-color: #2D3748; /* 默认背景色（墙壁色） */
  border: 2rpx solid rgba(255, 255, 255, 0.1);
}

/* 迷宫格子类型 */
.maze-cell.wall {
  background-color: #2D3748;
  border: 2rpx solid #4A5568;
}

.maze-cell.path {
  background-color: #ffffff !important;
  cursor: pointer;
  border: 2rpx solid #e2e8f0 !important;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.maze-cell.path:active {
  background-color: #f7fafc;
  transform: scale(0.95);
}

.maze-cell.visited {
  background-color: #e6fffa !important;
  border: 2rpx solid #38b2ac !important;
}

.maze-cell.has-player {
  background-color: #FFD700 !important;
  border: 6rpx solid #FFA500 !important;
  animation: pulse 1s infinite;
  z-index: 10;
}

.maze-cell.is-target {
  background-color: #48bb78 !important;
  border: 6rpx solid #38a169 !important;
  animation: glow 2s infinite;
  z-index: 10;
}

.maze-cell.has-character {
  background-color: #fef5e7 !important;
  border: 4rpx solid #d69e2e !important;
  color: #744210;
}

/* 迷宫内容 */
.cell-wall {
  width: 100%;
  height: 100%;
  background-color: #2D3748;
  border-radius: 2px;
}

.cell-path {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: transparent; /* 让父元素背景显示 */
  border-radius: 6rpx;
}

.cell-visited {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* 让父元素背景显示 */
}

.player-marker {
  font-size: 32rpx;
  background-color: #FFD700;
  color: #B45309;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 1s infinite;
  box-shadow: 0 0 10rpx #FFA500;
}

.target-marker {
  font-size: 32rpx;
  background-color: #48bb78;
  color: white;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: glow 2s infinite;
  box-shadow: 0 0 10rpx #10B981;
}

.cell-character {
  font-size: 14px;
  font-weight: bold;
  color: #1E3A8A;
}

.cell-character.visited {
  opacity: 0.6;
}

/* 特殊状态下的内部元素样式 */
.maze-cell.has-player .cell-path {
  background-color: transparent !important;
}

.maze-cell.is-target .cell-path {
  background-color: transparent !important;
}

.maze-cell.has-character .cell-path {
  background-color: transparent !important;
}

.maze-cell.visited .cell-path {
  background-color: transparent !important;
}

/* 当前成语显示 */
.current-idiom {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.idiom-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.idiom-display {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.idiom-char {
  width: 40px;
  height: 40px;
  border: 2px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.idiom-char.found {
  background-color: #10B981;
  color: white;
  border-color: #10B981;
}

.idiom-char.missing {
  background-color: #f8f9fa;
  color: #999;
}

/* 游戏控制按钮 */
.game-controls {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.control-btn {
  flex: 1;
  height: 100rpx;
  background-color: white;
  border: 2rpx solid #ddd;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.control-btn:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.control-btn[disabled] {
  opacity: 0.5;
  background-color: #f8f9fa;
}

.btn-icon {
  font-size: 36rpx;
  margin-bottom: 4rpx;
}

.btn-text {
  font-size: 24rpx;
  color: #666;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.idiom-modal,
.hint-modal,
.pause-modal {
  background-color: white;
  border-radius: 8px;
  width: 85%;
  max-width: 400px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #2D3748;
}

.modal-close {
  font-size: 20px;
  color: #999;
  padding: 8px;
  cursor: pointer;
}

/* 成语问题 */
.idiom-question {
  padding: 16px;
  text-align: center;
}

.question-text {
  font-size: 16px;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 12px;
}

.question-context {
  margin-top: 12px;
}

.context-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 选项列表 */
.idiom-options {
  padding: 0 16px;
  max-height: 300px;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.selected {
  border-color: #FFD700;
  background-color: rgba(255, 215, 0, 0.1);
}

.option-label {
  width: 30px;
  height: 30px;
  background-color: #1E3A8A;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.option-item.selected .option-label {
  background-color: #FFD700;
  color: #333;
}

.option-text {
  flex: 1;
  font-size: 16px;
}

.option-check {
  font-size: 18px;
  color: #10B981;
}

/* 弹窗操作按钮 */
.modal-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #eee;
}

.modal-actions .btn {
  flex: 1;
  height: 40px;
}

/* 提示内容 */
.hint-content {
  padding: 16px;
  text-align: center;
}

.hint-text {
  font-size: 16px;
  color: #2D3748;
  line-height: 1.6;
}

/* 暂停弹窗 */
.pause-modal {
  text-align: center;
}

.pause-content {
  padding: 20px;
}

.pause-title {
  font-size: 24px;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 12px;
}

.pause-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.pause-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pause-actions .btn {
  height: 44px;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px #10B981; }
  50% { box-shadow: 0 0 15px #10B981; }
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-3px); }
  80% { transform: translateY(-1px); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
