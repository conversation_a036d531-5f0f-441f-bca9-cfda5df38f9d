/* pages/game/game.wxss */

.game-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: var(--spacing-small);
  display: flex;
  flex-direction: column;
}

/* 游戏头部 */
.game-header {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-medium);
  margin-bottom: var(--spacing-small);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.level-text {
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-mini);
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 16px;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.star.active {
  opacity: 1;
}

.game-stats {
  display: flex;
  gap: var(--spacing-medium);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.stat-value {
  font-size: var(--font-size-small);
  font-weight: bold;
  color: var(--text-color);
}

/* 进度条 */
.progress-container {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-small);
  margin-bottom: var(--spacing-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-right: var(--spacing-small);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--font-size-small);
  font-weight: bold;
  color: var(--secondary-color);
  min-width: 40px;
}

/* 迷宫容器 */
.maze-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--spacing-medium);
}

.maze-grid {
  display: grid;
  gap: 1px;
  background-color: #2D3748;
  border-radius: var(--border-radius);
  padding: 2px;
  max-width: 90vw;
  max-height: 50vh;
  aspect-ratio: 1;
}

.maze-cell {
  aspect-ratio: 1;
  position: relative;
  border-radius: 2px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 迷宫格子类型 */
.maze-cell.wall {
  background-color: #2D3748;
}

.maze-cell.path {
  background-color: #f8f9fa;
  cursor: pointer;
}

.maze-cell.path:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.maze-cell.visited {
  background-color: #d4edda;
}

.maze-cell.has-player {
  background-color: var(--primary-color);
  animation: pulse 1s infinite;
}

.maze-cell.is-target {
  background-color: var(--success-color);
  animation: glow 2s infinite;
}

.maze-cell.has-character {
  background-color: #fff3cd;
  border: 2px solid var(--warning-color);
}

/* 迷宫内容 */
.cell-wall {
  width: 100%;
  height: 100%;
  background-color: #2D3748;
  border-radius: 2px;
}

.cell-path {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.cell-visited {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.player-marker {
  font-size: 16px;
  animation: bounce 1s infinite;
}

.target-marker {
  font-size: 14px;
  animation: rotate 2s linear infinite;
}

.cell-character {
  font-size: 14px;
  font-weight: bold;
  color: var(--secondary-color);
}

.cell-character.visited {
  opacity: 0.6;
}

/* 当前成语显示 */
.current-idiom {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-medium);
  margin-bottom: var(--spacing-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.idiom-label {
  font-size: var(--font-size-small);
  color: #666;
  margin-bottom: var(--spacing-small);
}

.idiom-display {
  display: flex;
  justify-content: center;
  gap: var(--spacing-small);
}

.idiom-char {
  width: 40px;
  height: 40px;
  border: 2px solid #ddd;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-medium);
  font-weight: bold;
  transition: all 0.3s ease;
}

.idiom-char.found {
  background-color: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.idiom-char.missing {
  background-color: #f8f9fa;
  color: #999;
}

/* 游戏控制按钮 */
.game-controls {
  display: flex;
  gap: var(--spacing-small);
  margin-bottom: var(--spacing-medium);
}

.control-btn {
  flex: 1;
  height: 50px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small);
  transition: all 0.2s ease;
}

.control-btn:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.control-btn[disabled] {
  opacity: 0.5;
  background-color: #f8f9fa;
}

.btn-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.btn-text {
  font-size: var(--font-size-mini);
  color: #666;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.idiom-modal,
.hint-modal,
.pause-modal {
  background-color: white;
  border-radius: var(--border-radius);
  width: 85%;
  max-width: 400px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-medium);
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--text-color);
}

.modal-close {
  font-size: 20px;
  color: #999;
  padding: var(--spacing-mini);
  cursor: pointer;
}

/* 成语问题 */
.idiom-question {
  padding: var(--spacing-medium);
  text-align: center;
}

.question-text {
  font-size: var(--font-size-medium);
  font-weight: bold;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-small);
}

.question-context {
  margin-top: var(--spacing-small);
}

.context-text {
  font-size: var(--font-size-small);
  color: #666;
  line-height: 1.6;
}

/* 选项列表 */
.idiom-options {
  padding: 0 var(--spacing-medium);
  max-height: 300px;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-small);
  margin-bottom: var(--spacing-small);
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.selected {
  border-color: var(--primary-color);
  background-color: rgba(255, 215, 0, 0.1);
}

.option-label {
  width: 30px;
  height: 30px;
  background-color: var(--secondary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: var(--spacing-small);
}

.option-item.selected .option-label {
  background-color: var(--primary-color);
  color: #333;
}

.option-text {
  flex: 1;
  font-size: var(--font-size-medium);
}

.option-check {
  font-size: 18px;
  color: var(--success-color);
}

/* 弹窗操作按钮 */
.modal-actions {
  display: flex;
  gap: var(--spacing-small);
  padding: var(--spacing-medium);
  border-top: 1px solid #eee;
}

.modal-actions .btn {
  flex: 1;
  height: 40px;
}

/* 提示内容 */
.hint-content {
  padding: var(--spacing-medium);
  text-align: center;
}

.hint-text {
  font-size: var(--font-size-medium);
  color: var(--text-color);
  line-height: 1.6;
}

/* 暂停弹窗 */
.pause-modal {
  text-align: center;
}

.pause-content {
  padding: var(--spacing-large);
}

.pause-title {
  font-size: 24px;
  font-weight: bold;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-small);
}

.pause-subtitle {
  font-size: var(--font-size-medium);
  color: #666;
  margin-bottom: var(--spacing-large);
}

.pause-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-small);
}

.pause-actions .btn {
  height: 44px;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px var(--success-color); }
  50% { box-shadow: 0 0 15px var(--success-color); }
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-3px); }
  80% { transform: translateY(-1px); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
