<!-- pages/game/game.wxml -->
<view class="game-container">
  <!-- 游戏头部信息 -->
  <view class="game-header">
    <view class="level-info">
      <text class="level-text">关卡 {{currentLevel}}</text>
      <view class="stars">
        <text class="star {{index < currentStars ? 'active' : ''}}"
              wx:for="{{3}}" wx:key="index">⭐</text>
      </view>
    </view>

    <view class="game-stats">
      <view class="stat-item">
        <text class="stat-icon">💰</text>
        <text class="stat-value">{{score}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-icon">⏱️</text>
        <text class="stat-value">{{timeDisplay}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-icon">💡</text>
        <text class="stat-value">{{hintsUsed}}</text>
      </view>
    </view>
  </view>

  <!-- 进度条 -->
  <view class="progress-container">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progress}}%"></view>
    </view>
    <text class="progress-text">{{progress}}%</text>
  </view>

  <!-- 调试信息 -->
  <view style="background: yellow; padding: 10rpx; margin: 10rpx; font-size: 24rpx;">
    调试: mazeSize={{mazeSize}}, mazeData长度={{mazeData.length}}
  </view>

  <!-- 迷宫区域 -->
  <view class="maze-container">
    <view class="maze-grid" style="grid-template-columns: repeat({{mazeSize}}, 1fr);">
      <view class="maze-cell {{getCellClass(item)}}"
            wx:for="{{mazeData}}" wx:key="index"
            bindtap="onCellTap" data-index="{{index}}"
            data-row="{{item.row}}"
            data-col="{{item.col}}">
        
        <!-- 迷宫墙壁 -->
        <view wx:if="{{item.type === 'wall'}}" class="cell-wall"></view>
        
        <!-- 迷宫路径 -->
        <view wx:elif="{{item.type === 'path'}}" class="cell-path">
          <!-- 玩家位置 -->
          <view wx:if="{{item.hasPlayer}}" class="player-marker">🚶</view>
          <!-- 成语字符 -->
          <text wx:if="{{item.character}}" class="cell-character">{{item.character}}</text>
          <!-- 目标点 -->
          <view wx:if="{{item.isTarget}}" class="target-marker">🎯</view>
        </view>
        
        <!-- 已访问路径 -->
        <view wx:elif="{{item.type === 'visited'}}" class="cell-visited">
          <text wx:if="{{item.character}}" class="cell-character visited">{{item.character}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 当前成语显示 -->
  <view class="current-idiom" wx:if="{{currentIdiom}}">
    <text class="idiom-label">当前成语：</text>
    <view class="idiom-display">
      <text class="idiom-char {{char.found ? 'found' : 'missing'}}" 
            wx:for="{{currentIdiom.characters}}" wx:key="index">
        {{char.found ? char.value : '□'}}
      </text>
    </view>
  </view>

  <!-- 游戏控制按钮 -->
  <view class="game-controls">
    <button class="control-btn" bindtap="showHint" disabled="{{!canUseHint}}">
      <text class="btn-icon">💡</text>
      <text class="btn-text">提示</text>
    </button>

    <button class="control-btn" bindtap="resetLevel">
      <text class="btn-icon">🔄</text>
      <text class="btn-text">重置</text>
    </button>

    <button class="control-btn" bindtap="pauseGame">
      <text class="btn-icon">⏸️</text>
      <text class="btn-text">暂停</text>
    </button>
  </view>

  <!-- 成语选择弹窗 -->
  <view class="modal-overlay" wx:if="{{showIdiomModal}}" bindtap="hideIdiomModal">
    <view class="idiom-modal" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">选择正确的成语</text>
        <text class="modal-close" bindtap="hideIdiomModal">✕</text>
      </view>
      
      <view class="idiom-question">
        <text class="question-text">{{currentQuestion.question}}</text>
        <view class="question-context" wx:if="{{currentQuestion.context}}">
          <text class="context-text">{{currentQuestion.context}}</text>
        </view>
      </view>
      
      <view class="idiom-options">
        <view class="option-item {{option.selected ? 'selected' : ''}}" 
              wx:for="{{currentQuestion.options}}" wx:key="index"
              bindtap="selectOption" data-index="{{index}}">
          <text class="option-label">{{String.fromCharCode(65 + index)}}</text>
          <text class="option-text">{{item.text}}</text>
          <text class="option-check" wx:if="{{item.selected}}">✓</text>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="btn btn-outline" bindtap="hideIdiomModal">取消</button>
        <button class="btn btn-primary" bindtap="confirmAnswer" 
                disabled="{{!hasSelectedOption}}">确认</button>
      </view>
    </view>
  </view>

  <!-- 提示弹窗 -->
  <view class="modal-overlay" wx:if="{{showHintModal}}" bindtap="hideHintModal">
    <view class="hint-modal" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">游戏提示</text>
        <text class="modal-close" bindtap="hideHintModal">✕</text>
      </view>
      
      <view class="hint-content">
        <text class="hint-text">{{currentHint}}</text>
      </view>
      
      <view class="modal-actions">
        <button class="btn btn-primary" bindtap="useHint">使用提示 (-10分)</button>
        <button class="btn btn-outline" bindtap="hideHintModal">取消</button>
      </view>
    </view>
  </view>

  <!-- 暂停弹窗 -->
  <view class="modal-overlay" wx:if="{{isPaused}}" bindtap="resumeGame">
    <view class="pause-modal" catchtap="preventClose">
      <view class="pause-content">
        <text class="pause-title">游戏暂停</text>
        <text class="pause-subtitle">点击任意位置继续</text>
        
        <view class="pause-actions">
          <button class="btn btn-primary" bindtap="resumeGame">继续游戏</button>
          <button class="btn btn-outline" bindtap="backToHome">返回首页</button>
        </view>
      </view>
    </view>
  </view>
</view>
