// pages/game/game.js
const app = getApp();
const api = require('../../utils/api.js');
const gameApi = api.gameApi;
const idiomApi = api.idiomApi;

Page({
  data: {
    // 游戏基本信息
    currentLevel: 1,
    currentStars: 0,
    score: 0,
    timeElapsed: 0,
    timeDisplay: '00:00',
    hintsUsed: 0,
    progress: 0,
    
    // 迷宫相关
    mazeSize: 5,
    mazeData: [],
    playerPosition: { row: 0, col: 0 },
    targetPosition: { row: 4, col: 4 },
    
    // 成语相关
    currentIdiom: null,
    targetIdioms: [],
    foundIdioms: [],
    collectedCharacters: [], // 已收集的字符（按顺序）
    
    // 游戏状态
    gameStarted: false,
    gameCompleted: false,
    isPaused: false,
    canUseHint: true,
    
    // 弹窗状态
    showIdiomModal: false,
    showHintModal: false,
    currentQuestion: null,
    currentHint: '',
    hasSelectedOption: false,
    
    // 游戏计时器
    gameTimer: null
  },

  onLoad(options) {
    console.log('游戏页面加载', options);
    const level = parseInt(options.level) || 1;
    const continueGame = options.continue === 'true';
    
    this.setData({ currentLevel: level });
    
    if (continueGame) {
      this.loadGameProgress();
    } else {
      this.initNewGame();
    }
  },

  onShow() {
    // 页面显示时恢复游戏计时器
    if (this.data.gameStarted && !this.data.isPaused && !this.data.gameCompleted) {
      this.startGameTimer();
    }
  },

  onHide() {
    // 页面隐藏时暂停游戏并保存进度
    this.pauseGameTimer();
    this.saveGameProgress();
  },

  onUnload() {
    // 页面卸载时清理计时器
    this.pauseGameTimer();
    this.saveGameProgress();
  },

  // 初始化新游戏
  initNewGame() {
    console.log('初始化新游戏，关卡:', this.data.currentLevel);
    
    // 重置游戏数据
    this.setData({
      score: 0,
      timeElapsed: 0,
      timeDisplay: '00:00',
      hintsUsed: 0,
      progress: 0,
      currentStars: 0,
      gameStarted: false,
      gameCompleted: false,
      isPaused: false,
      foundIdioms: []
    });
    
    // 生成关卡数据（使用后台数据）
    console.log('开始生成关卡，使用后台数据');
    this.generateLevel();
  },

  // 生成关卡
  generateLevel: function() {
    const level = this.data.currentLevel;
    const self = this;

    console.log('开始生成关卡:', level);

    // 先尝试从后端获取关卡信息
    gameApi.getLevel(level).then(function(levelData) {
      console.log('获取关卡信息成功:', levelData);

      // 解析目标成语
      const targetIdiomsData = JSON.parse(levelData.target_idioms || '[]');
      const targetIdioms = targetIdiomsData.map(function(idiom) {
        return {
          idiom: idiom,
          characters: idiom.split('')
        };
      });

      console.log(`关卡${level}的成语:`, targetIdioms);

      self.setData({
        mazeSize: levelData.maze_size || 5,
        targetIdioms: targetIdioms
      });

      // 生成迷宫
      self.generateMaze();

    }).catch(function(error) {
      console.error('获取关卡信息失败:', error);
      console.log('使用默认关卡配置');

      // 使用默认配置生成迷宫
      self.generateMazeDefault();
    });
  },

  // 生成迷宫（使用API）
  generateMaze: function() {
    const self = this;

    // 尝试从后端生成迷宫
    console.log('调用后端API生成迷宫，关卡:', this.data.currentLevel);
    gameApi.generateMaze(this.data.mazeSize, this.data.currentLevel).then(function(mazeData) {
      console.log('后端API返回的迷宫数据:', mazeData);

      // 检查迷宫数据格式
      if (!mazeData || !mazeData.cells) {
        console.error('迷宫数据格式错误，缺少cells字段');
        self.generateMazeDefault();
        return;
      }


      // 转换后端数据格式为前端格式
      const maze = [];
      for (let i = 0; i < mazeData.cells.length; i++) {
        for (let j = 0; j < mazeData.cells[i].length; j++) {
          const cell = mazeData.cells[i][j];
          maze.push({
            type: cell.type,
            row: cell.row,
            col: cell.col,
            hasPlayer: cell.is_start,
            isTarget: cell.is_end,
            character: cell.character || '',
            visited: false
          });
        }
      }

      console.log('转换后的迷宫数据:', maze);

      // 设置迷宫数据
      self.setData({
        mazeSize: mazeData.size,
        mazeData: maze,
        playerPosition: { row: 0, col: 0 }
      });

      // 启动游戏
      self.startGame();

    }).catch(function(error) {
      console.error('生成迷宫失败:', error);
      console.error('错误详情:', error.message || error);
      // 使用默认迷宫生成作为备用方案
      self.generateMazeDefault();
    });

    // 从后端生成迷宫（暂时禁用）
    console.log('调用后端API生成迷宫...');
    gameApi.generateMaze(this.data.mazeSize, 1).then(function(response) {
      console.log('后端API返回的原始响应:', response);
      console.log('响应类型:', typeof response);
      console.log('响应结构:', Object.keys(response || {}));

      // 检查响应格式
      if (!response || response.code !== 0 || !response.data) {
        console.error('API返回格式错误:', response);
        self.generateMazeDefault();
        return;
      }

      const mazeData = response.data;
      console.log('迷宫数据:', mazeData);

      // 检查迷宫数据格式
      if (!mazeData || !mazeData.cells) {
        console.error('迷宫数据格式错误，缺少cells字段');
        self.generateMazeDefault();
        return;
      }

      // 转换迷宫数据格式
      const maze = [];
      for (let i = 0; i < mazeData.size; i++) {
        for (let j = 0; j < mazeData.size; j++) {
          const cell = mazeData.cells[i][j];
          console.log(`处理格子[${i}][${j}]:`, cell);

          maze.push({
            type: cell.type,
            row: cell.row,
            col: cell.col,
            hasPlayer: cell.is_start,
            isTarget: cell.is_end,
            character: cell.character || '',
            visited: false
          });
        }
      }

      console.log('转换后的迷宫数据:', maze);

      self.setData({
        mazeData: maze,
        playerPosition: { row: 0, col: 0 }
      });

      // 迷宫生成完成，启动游戏
      self.startGame();

    }).catch(function(error) {
      console.error('生成迷宫失败:', error);
      console.error('错误详情:', error.message || error);
      // 使用默认迷宫生成
      self.generateMazeDefault();
    });
  },

  // 默认迷宫生成（备用方案）
  generateMazeDefault: function() {
    console.log('=== generateMazeDefault 开始执行 ===');

    // 强制设置为5x5迷宫
    const size = 5;
    const maze = [];

    console.log('迷宫大小:', size);

    // 确保有默认的目标成语（根据等级生成）
    if (!this.data.targetIdioms || this.data.targetIdioms.length === 0) {
      const level = this.data.currentLevel || 1;
      const defaultIdioms = this.getDefaultIdiomsForLevel(level);

      this.setData({
        targetIdioms: defaultIdioms
      });

      console.log(`关卡${level}的默认成语:`, defaultIdioms);
    }

    // 创建一个简单的测试迷宫 - 全部都是通路（测试用）
    for (let i = 0; i < size * size; i++) {
      const row = Math.floor(i / size);
      const col = i % size;

      // 暂时全部设为通路，确保游戏可以正常进行
      let cellType = 'path';

      maze.push({
        type: cellType,
        row,
        col,
        hasPlayer: row === 0 && col === 0,
        isTarget: row === size - 1 && col === size - 1,
        character: '',
        visited: false
      });
    }

    // 确保起点和终点是通路
    maze[0].type = 'path';
    maze[size * size - 1].type = 'path';

    // 在迷宫中放置成语字符
    this.placeIdiomCharacters(maze);

    console.log('生成的迷宫数据:', maze);

    // 统计迷宫格子类型
    const pathCount = maze.filter(cell => cell.type === 'path').length;
    const wallCount = maze.filter(cell => cell.type === 'wall').length;
    console.log(`迷宫统计: 通路${pathCount}个, 墙壁${wallCount}个`);

    // 检查前几个格子的数据
    console.log('前5个格子数据:', maze.slice(0, 5));

    this.setData({
      mazeSize: size,
      mazeData: maze,
      playerPosition: { row: 0, col: 0 }
    });

    // 验证setData后的数据
    console.log('setData后的数据:');
    console.log('- mazeSize:', this.data.mazeSize);
    console.log('- mazeData长度:', this.data.mazeData.length);
    console.log('- 前5个格子:', this.data.mazeData.slice(0, 5));
    console.log('- playerPosition:', this.data.playerPosition);

    // 迷宫生成完成，启动游戏
    this.startGame();
  },

  // 根据等级获取默认成语
  getDefaultIdiomsForLevel: function(level) {
    const idiomsByLevel = {
      1: [
        { idiom: '画蛇添足', characters: ['画', '蛇', '添', '足'] },
        { idiom: '守株待兔', characters: ['守', '株', '待', '兔'] }
      ],
      2: [
        { idiom: '亡羊补牢', characters: ['亡', '羊', '补', '牢'] },
        { idiom: '杯弓蛇影', characters: ['杯', '弓', '蛇', '影'] }
      ],
      3: [
        { idiom: '刻舟求剑', characters: ['刻', '舟', '求', '剑'] },
        { idiom: '掩耳盗铃', characters: ['掩', '耳', '盗', '铃'] }
      ],
      4: [
        { idiom: '井底之蛙', characters: ['井', '底', '之', '蛙'] },
        { idiom: '狐假虎威', characters: ['狐', '假', '虎', '威'] }
      ],
      5: [
        { idiom: '叶公好龙', characters: ['叶', '公', '好', '龙'] },
        { idiom: '买椟还珠', characters: ['买', '椟', '还', '珠'] }
      ],
      6: [
        { idiom: '南辕北辙', characters: ['南', '辕', '北', '辙'] },
        { idiom: '滥竽充数', characters: ['滥', '竽', '充', '数'] }
      ],
      7: [
        { idiom: '塞翁失马', characters: ['塞', '翁', '失', '马'] },
        { idiom: '邯郸学步', characters: ['邯', '郸', '学', '步'] }
      ],
      8: [
        { idiom: '班门弄斧', characters: ['班', '门', '弄', '斧'] },
        { idiom: '东施效颦', characters: ['东', '施', '效', '颦'] }
      ],
      9: [
        { idiom: '鹬蚌相争', characters: ['鹬', '蚌', '相', '争'] },
        { idiom: '螳螂捕蝉', characters: ['螳', '螂', '捕', '蝉'] }
      ],
      10: [
        { idiom: '庖丁解牛', characters: ['庖', '丁', '解', '牛'] },
        { idiom: '愚公移山', characters: ['愚', '公', '移', '山'] }
      ]
    };

    // 如果等级超出预设范围，使用最高等级的成语
    const maxLevel = Math.max(...Object.keys(idiomsByLevel).map(Number));
    const targetLevel = Math.min(level, maxLevel);

    return idiomsByLevel[targetLevel] || idiomsByLevel[1];
  },

  // 在迷宫中放置成语字符
  placeIdiomCharacters: function(maze) {
    const targetIdioms = this.data.targetIdioms;
    const mazeSize = this.data.mazeSize;
    const pathCells = maze.filter(function(cell) {
      return cell.type === 'path' && !cell.hasPlayer && !cell.isTarget;
    });

    // 为每个目标成语放置字符
    targetIdioms.forEach(function(idiom) {
      idiom.characters.forEach(function(char) {
        if (pathCells.length > 0) {
          const randomIndex = Math.floor(Math.random() * pathCells.length);
          const cell = pathCells.splice(randomIndex, 1)[0];
          cell.character = char;
        }
      });
    });
  },

  // 开始游戏
  startGame() {
    console.log('游戏开始，迷宫数据:', this.data.mazeData.length);

    this.setData({
      gameStarted: true,
      gameCompleted: false
    });

    this.startGameTimer();

    wx.showToast({
      title: '游戏开始！',
      icon: 'success',
      duration: 1500
    });

    console.log('游戏状态已更新:', {
      gameStarted: this.data.gameStarted,
      mazeSize: this.data.mazeSize,
      playerPosition: this.data.playerPosition
    });

    // 输出玩家周围的格子信息，帮助调试
    const { row, col } = this.data.playerPosition;
    const size = this.data.mazeSize;
    console.log('玩家周围格子:');
    console.log('- 上方:', row > 0 ? this.data.mazeData[(row-1) * size + col] : '边界');
    console.log('- 下方:', row < size-1 ? this.data.mazeData[(row+1) * size + col] : '边界');
    console.log('- 左方:', col > 0 ? this.data.mazeData[row * size + (col-1)] : '边界');
    console.log('- 右方:', col < size-1 ? this.data.mazeData[row * size + (col+1)] : '边界');
  },

  // 开始游戏计时器
  startGameTimer() {
    this.pauseGameTimer(); // 先清除可能存在的计时器
    
    this.data.gameTimer = setInterval(() => {
      if (!this.data.isPaused && !this.data.gameCompleted) {
        const timeElapsed = this.data.timeElapsed + 1;
        const minutes = Math.floor(timeElapsed / 60);
        const seconds = timeElapsed % 60;
        const timeDisplay = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        this.setData({
          timeElapsed,
          timeDisplay
        });
      }
    }, 1000);
  },

  // 暂停游戏计时器
  pauseGameTimer() {
    if (this.data.gameTimer) {
      clearInterval(this.data.gameTimer);
      this.data.gameTimer = null;
    }
  },

  // 处理迷宫格子点击
  onCellTap(e) {
    console.log('格子被点击', {
      gameStarted: this.data.gameStarted,
      isPaused: this.data.isPaused,
      gameCompleted: this.data.gameCompleted
    });

    if (!this.data.gameStarted || this.data.isPaused || this.data.gameCompleted) {
      console.log('游戏未启动或已暂停/完成，无法移动');
      return;
    }

    const { index, row, col } = e.currentTarget.dataset;
    const cell = this.data.mazeData[index];

    console.log('点击位置原始数据:', {
      index,
      row,
      col,
      indexType: typeof index,
      rowType: typeof row,
      colType: typeof col,
      cell
    });

    // 确保数据转换正确
    const targetRow = parseInt(row);
    const targetCol = parseInt(col);

    console.log('转换后的数据:', {
      targetRow,
      targetCol,
      targetRowType: typeof targetRow,
      targetColType: typeof targetCol,
      isNaN_row: isNaN(targetRow),
      isNaN_col: isNaN(targetCol)
    });

    // 检查是否可以移动到该位置
    if (this.canMoveTo(targetRow, targetCol)) {
      console.log('可以移动到该位置');
      this.movePlayer(targetRow, targetCol);
    } else {
      console.log('无法移动到该位置');
    }
  },

  // 检查是否可以移动到指定位置
  canMoveTo(targetRow, targetCol) {
    const { playerPosition } = this.data;
    const { row: currentRow, col: currentCol } = playerPosition;

    console.log('canMoveTo 输入参数:', {
      targetRow,
      targetCol,
      targetRowType: typeof targetRow,
      targetColType: typeof targetCol,
      playerPosition
    });

    // 检查输入参数是否有效
    if (targetRow === undefined || targetCol === undefined ||
        isNaN(targetRow) || isNaN(targetCol)) {
      console.log('输入参数无效');
      return false;
    }

    // 确保当前位置数据有效
    if (currentRow === undefined || currentCol === undefined ||
        isNaN(currentRow) || isNaN(currentCol)) {
      console.log('玩家位置数据无效:', { currentRow, currentCol });
      return false;
    }

    // 转换为数字（输入参数已经是数字了）
    const targetR = Number(targetRow);
    const targetC = Number(targetCol);
    const currentR = Number(currentRow);
    const currentC = Number(currentCol);

    console.log('移动检查:', {
      current: { row: currentR, col: currentC },
      target: { row: targetR, col: targetC }
    });

    // 检查是否相邻
    const rowDiff = Math.abs(targetR - currentR);
    const colDiff = Math.abs(targetC - currentC);

    console.log('距离计算:', { rowDiff, colDiff });

    if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
      // 检查目标位置是否是通路
      const targetIndex = targetR * this.data.mazeSize + targetC;
      const targetCell = this.data.mazeData[targetIndex];

      console.log('目标格子:', { targetIndex, targetCell });

      return targetCell && targetCell.type === 'path';
    }

    console.log('不相邻，无法移动');
    return false;
  },

  // 移动玩家
  movePlayer(newRow, newCol) {
    const { playerPosition, mazeSize } = this.data;
    const oldIndex = playerPosition.row * mazeSize + playerPosition.col;
    const newIndex = newRow * mazeSize + newCol;
    
    // 更新迷宫数据
    const mazeData = [...this.data.mazeData];
    mazeData[oldIndex].hasPlayer = false;
    mazeData[newIndex].hasPlayer = true;
    
    // 检查是否收集到成语字符
    if (mazeData[newIndex].character) {
      this.collectCharacter(mazeData[newIndex].character);
      mazeData[newIndex].character = ''; // 移除已收集的字符
    }
    
    // 检查是否到达终点
    if (mazeData[newIndex].isTarget) {
      this.reachTarget();
    }
    
    this.setData({
      mazeData,
      playerPosition: { row: newRow, col: newCol }
    });
    
    // 添加移动音效
    if (app.globalData.settings.soundEnabled) {
      wx.playBackgroundAudio({
        dataUrl: '/sounds/move.mp3'
      });
    }
  },

  // 收集成语字符
  collectCharacter(character) {
    console.log('收集到字符:', character);

    // 添加到已收集字符列表
    const collectedCharacters = [...this.data.collectedCharacters, character];
    this.setData({ collectedCharacters });

    console.log('当前已收集字符:', collectedCharacters);

    // 检查是否完成了某个成语
    this.checkIdiomCompletion(collectedCharacters);

    // 更新分数
    this.addScore(10);

    // 震动反馈
    if (app.globalData.settings.vibrationEnabled) {
      wx.vibrateShort();
    }
  },

  // 检查成语完成情况
  checkIdiomCompletion(collectedCharacters) {
    const targetIdioms = this.data.targetIdioms;
    const foundIdioms = this.data.foundIdioms;

    console.log('检查成语完成情况:', {
      collected: collectedCharacters,
      targets: targetIdioms.map(i => i.idiom),
      alreadyFound: foundIdioms
    });

    // 检查每个目标成语
    for (let idiom of targetIdioms) {
      const idiomChars = idiom.characters;

      // 跳过已经完成的成语
      if (foundIdioms.includes(idiom.idiom)) {
        console.log(`成语"${idiom.idiom}"已完成，跳过检查`);
        continue;
      }

      // 检查是否按正确顺序收集了完整的成语
      if (this.isIdiomCompleted(collectedCharacters, idiomChars)) {
        console.log('新完成成语:', idiom.idiom);

        // 显示成语完成提示
        this.showIdiomCompletedMessage(idiom.idiom);

        // 添加到已完成成语列表
        const newFoundIdioms = [...foundIdioms, idiom.idiom];
        this.setData({ foundIdioms: newFoundIdioms });

        // 额外奖励分数
        this.addScore(50);

        // 检查是否完成所有成语
        if (newFoundIdioms.length === targetIdioms.length) {
          console.log('所有成语已完成！');
          this.showAllIdiomsCompleted();
        }

        break; // 一次只处理一个完成的成语
      }
    }
  },

  // 检查成语是否按正确顺序完成
  isIdiomCompleted(collectedChars, idiomChars) {
    console.log('检查成语完成:', {
      collected: collectedChars,
      target: idiomChars,
      targetIdiom: idiomChars.join('')
    });

    // 使用严格的连续子序列匹配
    return this.hasConsecutiveSubsequence(collectedChars, idiomChars);
  },

  // 检查是否包含连续的子序列（必须按正确顺序且连续）
  hasConsecutiveSubsequence(collected, target) {
    if (target.length === 0) return true;
    if (collected.length < target.length) return false;

    let targetIndex = 0;

    for (let i = 0; i < collected.length; i++) {
      if (collected[i] === target[targetIndex]) {
        targetIndex++;

        // 如果找到了完整的序列
        if (targetIndex === target.length) {
          console.log(`找到完整成语序列: ${target.join('')}`);
          return true;
        }
      } else {
        // 如果当前字符不匹配，重置匹配进度
        // 但要检查当前字符是否是目标序列的开始
        if (collected[i] === target[0]) {
          targetIndex = 1; // 从第二个字符开始匹配
        } else {
          targetIndex = 0; // 完全重置
        }
      }
    }

    console.log(`成语未完成，已匹配: ${targetIndex}/${target.length}`);
    return false;
  },

  // 显示成语完成消息
  showIdiomCompletedMessage(idiom) {
    // 使用Modal而不是Toast，确保成语完整显示
    wx.showModal({
      title: '🎉 恭喜完成成语！',
      content: `您成功完成了成语：\n\n"${idiom}"\n\n获得50分奖励！`,
      showCancel: false,
      confirmText: '继续游戏'
    });
  },

  // 显示所有成语完成消息
  showAllIdiomsCompleted() {
    const targetIdioms = this.data.targetIdioms;
    const idiomList = targetIdioms.map(item => `"${item.idiom}"`).join('、');

    wx.showModal({
      title: '🏆 全部完成！',
      content: `恭喜您！已完成所有成语：\n\n${idiomList}\n\n现在可以前往绿色终点完成关卡了！`,
      showCancel: false,
      confirmText: '前往终点'
    });
  },



  // 显示成语选择题
  showIdiomQuestion() {
    const questions = [
      {
        question: '请选择正确的成语：',
        context: '比喻做了多余的事，非但无益，反而不合适。',
        options: [
          { text: '画蛇添足', correct: true },
          { text: '守株待兔', correct: false },
          { text: '亡羊补牢', correct: false },
          { text: '杯弓蛇影', correct: false }
        ]
      }
    ];
    
    const currentQuestion = questions[0];
    currentQuestion.options.forEach(option => option.selected = false);
    
    this.setData({
      showIdiomModal: true,
      currentQuestion,
      hasSelectedOption: false
    });
  },

  // 选择答案选项
  selectOption(e) {
    const index = e.currentTarget.dataset.index;
    const currentQuestion = { ...this.data.currentQuestion };
    
    // 重置所有选项
    currentQuestion.options.forEach(option => option.selected = false);
    // 选中当前选项
    currentQuestion.options[index].selected = true;
    
    this.setData({
      currentQuestion,
      hasSelectedOption: true
    });
  },

  // 确认答案
  confirmAnswer() {
    const { currentQuestion } = this.data;
    const selectedOption = currentQuestion.options.find(option => option.selected);
    
    if (selectedOption) {
      if (selectedOption.correct) {
        // 答对了
        this.addScore(50);
        wx.showToast({
          title: '回答正确！',
          icon: 'success'
        });
      } else {
        // 答错了
        wx.showToast({
          title: '回答错误！',
          icon: 'error'
        });
      }
      
      this.hideIdiomModal();
    }
  },

  // 隐藏成语选择弹窗
  hideIdiomModal() {
    this.setData({
      showIdiomModal: false,
      currentQuestion: null,
      hasSelectedOption: false
    });
  },

  // 添加分数
  addScore(points) {
    const newScore = this.data.score + points;
    this.setData({ score: newScore });
    
    // 更新进度
    this.updateProgress();
  },

  // 更新游戏进度
  updateProgress() {
    // 简化版进度计算
    const progress = Math.min(100, (this.data.score / 200) * 100);
    this.setData({ progress });
  },

  // 到达终点
  reachTarget() {
    console.log('到达终点！');

    // 检查是否完成了所有成语
    const targetIdioms = this.data.targetIdioms;
    const foundIdioms = this.data.foundIdioms;

    console.log('终点检查:', {
      targetCount: targetIdioms.length,
      foundCount: foundIdioms.length,
      targetIdioms: targetIdioms.map(i => i.idiom),
      foundIdioms: foundIdioms
    });

    if (foundIdioms.length < targetIdioms.length) {
      // 还有成语未完成
      const remainingIdioms = targetIdioms
        .filter(idiom => !foundIdioms.includes(idiom.idiom))
        .map(idiom => idiom.idiom);

      const self = this;
      wx.showModal({
        title: '还未完成所有成语',
        content: `请先按正确顺序收集完成以下成语：\n${remainingIdioms.join('、')}\n\n点击"重新开始"将重置当前关卡`,
        showCancel: true,
        cancelText: '重新开始',
        confirmText: '继续游戏',
        success: function(res) {
          if (res.cancel) {
            // 用户选择重新开始，重置当前关卡
            self.resetLevel();
          }
          // 如果用户选择继续游戏，什么都不做，让玩家继续收集成语
        }
      });

      return; // 不允许完成关卡
    }

    // 所有成语都已完成，可以完成关卡
    this.completeLevel();
  },

  // 完成关卡
  completeLevel() {
    this.pauseGameTimer();
    
    const { score, timeElapsed, hintsUsed } = this.data;
    
    // 计算星级评价
    let stars = 1;
    if (score >= 150 && hintsUsed <= 1) stars = 3;
    else if (score >= 100 && hintsUsed <= 2) stars = 2;
    
    this.setData({
      gameCompleted: true,
      currentStars: stars
    });
    
    // 更新用户数据
    this.updateUserProgress();
    
    // 显示完成提示
    setTimeout(() => {
      wx.showModal({
        title: '关卡完成！',
        content: `获得 ${stars} 星评价\n得分：${score}\n用时：${this.data.timeDisplay}`,
        confirmText: '下一关',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            this.nextLevel();
          } else {
            this.backToHome();
          }
        }
      });
    }, 1000);
  },

  // 下一关
  nextLevel() {
    const nextLevel = this.data.currentLevel + 1;
    wx.redirectTo({
      url: `/pages/game/game?level=${nextLevel}`
    });
  },

  // 返回首页
  backToHome() {
    wx.navigateBack();
  },

  // 更新用户进度
  updateUserProgress() {
    const { currentLevel, score, currentStars } = this.data;
    
    // 更新全局数据
    const gameData = app.globalData.gameData;
    gameData.totalScore += score;
    gameData.currentLevel = Math.max(gameData.currentLevel, currentLevel);
    gameData.unlockedLevels = Math.max(gameData.unlockedLevels, currentLevel + 1);
    
    app.updateGameData(gameData);
  },

  // 显示提示
  showHint() {
    if (!this.data.canUseHint) {
      wx.showToast({
        title: '提示次数已用完',
        icon: 'none'
      });
      return;
    }
    
    const hints = [
      '寻找迷宫中的成语字符',
      '相邻的格子才能移动',
      '收集完整的成语可以获得更多分数',
      '到达终点完成关卡'
    ];
    
    const randomHint = hints[Math.floor(Math.random() * hints.length)];
    
    this.setData({
      showHintModal: true,
      currentHint: randomHint
    });
  },

  // 使用提示
  useHint() {
    const hintsUsed = this.data.hintsUsed + 1;
    const newScore = Math.max(0, this.data.score - 10);
    
    this.setData({
      hintsUsed,
      score: newScore,
      canUseHint: hintsUsed < 3
    });
    
    this.hideHintModal();
    
    wx.showToast({
      title: '提示已使用',
      icon: 'success'
    });
  },

  // 隐藏提示弹窗
  hideHintModal() {
    this.setData({
      showHintModal: false,
      currentHint: ''
    });
  },

  // 重置关卡
  resetLevel() {
    wx.showModal({
      title: '确认重置',
      content: '重置关卡将清除当前进度，确定要重置吗？',
      success: (res) => {
        if (res.confirm) {
          this.initNewGame();
        }
      }
    });
  },

  // 暂停游戏
  pauseGame() {
    this.setData({ isPaused: true });
    this.pauseGameTimer();
  },

  // 恢复游戏
  resumeGame() {
    this.setData({ isPaused: false });
    if (this.data.gameStarted && !this.data.gameCompleted) {
      this.startGameTimer();
    }
  },

  // 获取格子样式类
  getCellClass(cell) {
    let classes = ['maze-cell'];

    if (cell.type === 'wall') classes.push('wall');
    else if (cell.type === 'path') classes.push('path');
    else if (cell.type === 'visited') classes.push('visited');

    if (cell.hasPlayer) classes.push('has-player');
    if (cell.isTarget) classes.push('is-target');
    if (cell.character) classes.push('has-character');

    const result = classes.join(' ');

    // 调试：输出前几个格子的样式类
    if (cell.row === 0 && cell.col < 3) {
      console.log(`格子[${cell.row}][${cell.col}] type=${cell.type} classes=${result}`);
    }

    return result;
  },

  // 保存游戏进度
  saveGameProgress() {
    const gameProgress = {
      level: this.data.currentLevel,
      score: this.data.score,
      timeElapsed: this.data.timeElapsed,
      hintsUsed: this.data.hintsUsed,
      mazeData: this.data.mazeData,
      playerPosition: this.data.playerPosition,
      foundIdioms: this.data.foundIdioms
    };
    
    try {
      wx.setStorageSync('gameProgress', gameProgress);
    } catch (error) {
      console.error('保存游戏进度失败:', error);
    }
  },

  // 加载游戏进度
  loadGameProgress() {
    try {
      const gameProgress = wx.getStorageSync('gameProgress');
      if (gameProgress) {
        this.setData({
          score: gameProgress.score || 0,
          timeElapsed: gameProgress.timeElapsed || 0,
          hintsUsed: gameProgress.hintsUsed || 0,
          mazeData: gameProgress.mazeData || [],
          playerPosition: gameProgress.playerPosition || { row: 0, col: 0 },
          foundIdioms: gameProgress.foundIdioms || []
        });
        
        this.startGame();
      } else {
        this.initNewGame();
      }
    } catch (error) {
      console.error('加载游戏进度失败:', error);
      this.initNewGame();
    }
  },

  // 阻止弹窗关闭
  preventClose() {
    // 空函数，阻止事件冒泡
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `我正在挑战成语迷宫第${this.data.currentLevel}关！`,
      path: `/pages/game/game?level=${this.data.currentLevel}`,
      imageUrl: '/images/share-game.jpg'
    };
  }
});
