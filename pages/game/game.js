// pages/game/game.js
const app = getApp();
const api = require('../../utils/api.js');
const gameApi = api.gameApi;
const idiomApi = api.idiomApi;

Page({
  data: {
    // 游戏基本信息
    currentLevel: 1,
    currentStars: 0,
    score: 0,
    timeElapsed: 0,
    timeDisplay: '00:00',
    hintsUsed: 0,
    progress: 0,
    
    // 迷宫相关
    mazeSize: 7,
    mazeData: [],
    playerPosition: { row: 0, col: 0 },
    targetPosition: { row: 6, col: 6 },
    
    // 成语相关
    currentIdiom: null,
    targetIdioms: [],
    foundIdioms: [],
    
    // 游戏状态
    gameStarted: false,
    gameCompleted: false,
    isPaused: false,
    canUseHint: true,
    
    // 弹窗状态
    showIdiomModal: false,
    showHintModal: false,
    currentQuestion: null,
    currentHint: '',
    hasSelectedOption: false,
    
    // 游戏计时器
    gameTimer: null
  },

  onLoad(options) {
    console.log('游戏页面加载', options);
    const level = parseInt(options.level) || 1;
    const continueGame = options.continue === 'true';
    
    this.setData({ currentLevel: level });
    
    if (continueGame) {
      this.loadGameProgress();
    } else {
      this.initNewGame();
    }
  },

  onShow() {
    // 页面显示时恢复游戏计时器
    if (this.data.gameStarted && !this.data.isPaused && !this.data.gameCompleted) {
      this.startGameTimer();
    }
  },

  onHide() {
    // 页面隐藏时暂停游戏并保存进度
    this.pauseGameTimer();
    this.saveGameProgress();
  },

  onUnload() {
    // 页面卸载时清理计时器
    this.pauseGameTimer();
    this.saveGameProgress();
  },

  // 初始化新游戏
  initNewGame() {
    console.log('初始化新游戏，关卡:', this.data.currentLevel);
    
    // 重置游戏数据
    this.setData({
      score: 0,
      timeElapsed: 0,
      timeDisplay: '00:00',
      hintsUsed: 0,
      progress: 0,
      currentStars: 0,
      gameStarted: false,
      gameCompleted: false,
      isPaused: false,
      foundIdioms: []
    });
    
    // 生成关卡数据
    this.generateLevel();
    
    // 开始游戏
    this.startGame();
  },

  // 生成关卡
  generateLevel: function() {
    const level = this.data.currentLevel;
    const self = this;

    console.log('开始生成关卡:', level);

    // 先尝试从后端获取关卡信息
    gameApi.getLevel(level).then(function(levelData) {
      console.log('获取关卡信息成功:', levelData);

      // 解析目标成语
      const targetIdiomsData = JSON.parse(levelData.target_idioms || '[]');
      const targetIdioms = targetIdiomsData.map(function(idiom) {
        return {
          idiom: idiom,
          characters: idiom.split('')
        };
      });

      self.setData({
        mazeSize: levelData.maze_size,
        targetIdioms: targetIdioms
      });

      // 生成迷宫
      self.generateMaze();

    }).catch(function(error) {
      console.error('获取关卡信息失败:', error);
      console.log('使用默认关卡配置');

      // 设置默认关卡配置
      const defaultTargetIdioms = [
        { idiom: '画蛇添足', characters: ['画', '蛇', '添', '足'] },
        { idiom: '守株待兔', characters: ['守', '株', '待', '兔'] }
      ];

      self.setData({
        mazeSize: 5 + Math.floor(level / 3), // 根据关卡动态调整大小
        targetIdioms: defaultTargetIdioms
      });

      // 使用默认配置生成迷宫
      self.generateMazeDefault();
    });
  },

  // 生成迷宫（使用API）
  generateMaze: function() {
    const self = this;

    // 从后端生成迷宫
    gameApi.generateMaze(this.data.mazeSize, 1).then(function(mazeData) {
      // 转换迷宫数据格式
      const maze = [];
      for (let i = 0; i < mazeData.size; i++) {
        for (let j = 0; j < mazeData.size; j++) {
          const cell = mazeData.cells[i][j];
          maze.push({
            type: cell.type,
            row: cell.row,
            col: cell.col,
            hasPlayer: cell.is_start,
            isTarget: cell.is_end,
            character: cell.character,
            visited: false
          });
        }
      }

      self.setData({
        mazeData: maze,
        playerPosition: { row: 0, col: 0 }
      });

    }).catch(function(error) {
      console.error('生成迷宫失败:', error);
      // 使用默认迷宫生成
      self.generateMazeDefault();
    });
  },

  // 默认迷宫生成（备用方案）
  generateMazeDefault: function() {
    const size = this.data.mazeSize || 5; // 默认5x5迷宫
    const maze = [];

    // 确保有默认的目标成语
    if (!this.data.targetIdioms || this.data.targetIdioms.length === 0) {
      this.setData({
        targetIdioms: [
          { idiom: '画蛇添足', characters: ['画', '蛇', '添', '足'] },
          { idiom: '守株待兔', characters: ['守', '株', '待', '兔'] }
        ]
      });
    }
    
    // 初始化迷宫（简化版迷宫生成）
    for (let i = 0; i < size * size; i++) {
      const row = Math.floor(i / size);
      const col = i % size;
      
      let cellType = 'path';
      
      // 边界设为墙壁
      if (row === 0 || row === size - 1 || col === 0 || col === size - 1) {
        if (!(row === 0 && col === 0) && !(row === size - 1 && col === size - 1)) {
          cellType = Math.random() < 0.3 ? 'wall' : 'path';
        }
      } else {
        // 内部随机生成墙壁
        cellType = Math.random() < 0.25 ? 'wall' : 'path';
      }
      
      maze.push({
        type: cellType,
        row,
        col,
        hasPlayer: row === 0 && col === 0,
        isTarget: row === size - 1 && col === size - 1,
        character: '',
        visited: false
      });
    }
    
    // 确保起点和终点是通路
    maze[0].type = 'path';
    maze[size * size - 1].type = 'path';
    
    // 在迷宫中放置成语字符
    this.placeIdiomCharacters(maze);
    
    this.setData({
      mazeData: maze,
      playerPosition: { row: 0, col: 0 }
    });
  },

  // 在迷宫中放置成语字符
  placeIdiomCharacters: function(maze) {
    const targetIdioms = this.data.targetIdioms;
    const mazeSize = this.data.mazeSize;
    const pathCells = maze.filter(function(cell) {
      return cell.type === 'path' && !cell.hasPlayer && !cell.isTarget;
    });

    // 为每个目标成语放置字符
    targetIdioms.forEach(function(idiom) {
      idiom.characters.forEach(function(char) {
        if (pathCells.length > 0) {
          const randomIndex = Math.floor(Math.random() * pathCells.length);
          const cell = pathCells.splice(randomIndex, 1)[0];
          cell.character = char;
        }
      });
    });
  },

  // 开始游戏
  startGame() {
    this.setData({
      gameStarted: true,
      gameCompleted: false
    });
    
    this.startGameTimer();
    
    wx.showToast({
      title: '游戏开始！',
      icon: 'success',
      duration: 1500
    });
  },

  // 开始游戏计时器
  startGameTimer() {
    this.pauseGameTimer(); // 先清除可能存在的计时器
    
    this.data.gameTimer = setInterval(() => {
      if (!this.data.isPaused && !this.data.gameCompleted) {
        const timeElapsed = this.data.timeElapsed + 1;
        const minutes = Math.floor(timeElapsed / 60);
        const seconds = timeElapsed % 60;
        const timeDisplay = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        this.setData({
          timeElapsed,
          timeDisplay
        });
      }
    }, 1000);
  },

  // 暂停游戏计时器
  pauseGameTimer() {
    if (this.data.gameTimer) {
      clearInterval(this.data.gameTimer);
      this.data.gameTimer = null;
    }
  },

  // 处理迷宫格子点击
  onCellTap(e) {
    if (!this.data.gameStarted || this.data.isPaused || this.data.gameCompleted) {
      return;
    }
    
    const { index, row, col } = e.currentTarget.dataset;
    const cell = this.data.mazeData[index];
    
    // 检查是否可以移动到该位置
    if (this.canMoveTo(row, col)) {
      this.movePlayer(row, col);
    }
  },

  // 检查是否可以移动到指定位置
  canMoveTo(targetRow, targetCol) {
    const { playerPosition } = this.data;
    const { row: currentRow, col: currentCol } = playerPosition;
    
    // 检查是否相邻
    const rowDiff = Math.abs(targetRow - currentRow);
    const colDiff = Math.abs(targetCol - currentCol);
    
    if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
      // 检查目标位置是否是通路
      const targetIndex = targetRow * this.data.mazeSize + targetCol;
      const targetCell = this.data.mazeData[targetIndex];
      
      return targetCell.type === 'path';
    }
    
    return false;
  },

  // 移动玩家
  movePlayer(newRow, newCol) {
    const { playerPosition, mazeSize } = this.data;
    const oldIndex = playerPosition.row * mazeSize + playerPosition.col;
    const newIndex = newRow * mazeSize + newCol;
    
    // 更新迷宫数据
    const mazeData = [...this.data.mazeData];
    mazeData[oldIndex].hasPlayer = false;
    mazeData[newIndex].hasPlayer = true;
    
    // 检查是否收集到成语字符
    if (mazeData[newIndex].character) {
      this.collectCharacter(mazeData[newIndex].character);
      mazeData[newIndex].character = ''; // 移除已收集的字符
    }
    
    // 检查是否到达终点
    if (mazeData[newIndex].isTarget) {
      this.reachTarget();
    }
    
    this.setData({
      mazeData,
      playerPosition: { row: newRow, col: newCol }
    });
    
    // 添加移动音效
    if (app.globalData.settings.soundEnabled) {
      wx.playBackgroundAudio({
        dataUrl: '/sounds/move.mp3'
      });
    }
  },

  // 收集成语字符
  collectCharacter(character) {
    console.log('收集到字符:', character);
    
    // 检查是否完成了某个成语
    this.checkIdiomCompletion(character);
    
    // 更新分数
    this.addScore(10);
    
    // 震动反馈
    if (app.globalData.settings.vibrationEnabled) {
      wx.vibrateShort();
    }
  },

  // 检查成语完成情况
  checkIdiomCompletion(newCharacter) {
    // 这里应该实现成语匹配逻辑
    // 简化版：随机触发成语选择
    if (Math.random() < 0.3) {
      this.showIdiomQuestion();
    }
  },

  // 显示成语选择题
  showIdiomQuestion() {
    const questions = [
      {
        question: '请选择正确的成语：',
        context: '比喻做了多余的事，非但无益，反而不合适。',
        options: [
          { text: '画蛇添足', correct: true },
          { text: '守株待兔', correct: false },
          { text: '亡羊补牢', correct: false },
          { text: '杯弓蛇影', correct: false }
        ]
      }
    ];
    
    const currentQuestion = questions[0];
    currentQuestion.options.forEach(option => option.selected = false);
    
    this.setData({
      showIdiomModal: true,
      currentQuestion,
      hasSelectedOption: false
    });
  },

  // 选择答案选项
  selectOption(e) {
    const index = e.currentTarget.dataset.index;
    const currentQuestion = { ...this.data.currentQuestion };
    
    // 重置所有选项
    currentQuestion.options.forEach(option => option.selected = false);
    // 选中当前选项
    currentQuestion.options[index].selected = true;
    
    this.setData({
      currentQuestion,
      hasSelectedOption: true
    });
  },

  // 确认答案
  confirmAnswer() {
    const { currentQuestion } = this.data;
    const selectedOption = currentQuestion.options.find(option => option.selected);
    
    if (selectedOption) {
      if (selectedOption.correct) {
        // 答对了
        this.addScore(50);
        wx.showToast({
          title: '回答正确！',
          icon: 'success'
        });
      } else {
        // 答错了
        wx.showToast({
          title: '回答错误！',
          icon: 'error'
        });
      }
      
      this.hideIdiomModal();
    }
  },

  // 隐藏成语选择弹窗
  hideIdiomModal() {
    this.setData({
      showIdiomModal: false,
      currentQuestion: null,
      hasSelectedOption: false
    });
  },

  // 添加分数
  addScore(points) {
    const newScore = this.data.score + points;
    this.setData({ score: newScore });
    
    // 更新进度
    this.updateProgress();
  },

  // 更新游戏进度
  updateProgress() {
    // 简化版进度计算
    const progress = Math.min(100, (this.data.score / 200) * 100);
    this.setData({ progress });
  },

  // 到达终点
  reachTarget() {
    console.log('到达终点！');
    this.completeLevel();
  },

  // 完成关卡
  completeLevel() {
    this.pauseGameTimer();
    
    const { score, timeElapsed, hintsUsed } = this.data;
    
    // 计算星级评价
    let stars = 1;
    if (score >= 150 && hintsUsed <= 1) stars = 3;
    else if (score >= 100 && hintsUsed <= 2) stars = 2;
    
    this.setData({
      gameCompleted: true,
      currentStars: stars
    });
    
    // 更新用户数据
    this.updateUserProgress();
    
    // 显示完成提示
    setTimeout(() => {
      wx.showModal({
        title: '关卡完成！',
        content: `获得 ${stars} 星评价\n得分：${score}\n用时：${this.data.timeDisplay}`,
        confirmText: '下一关',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            this.nextLevel();
          } else {
            this.backToHome();
          }
        }
      });
    }, 1000);
  },

  // 下一关
  nextLevel() {
    const nextLevel = this.data.currentLevel + 1;
    wx.redirectTo({
      url: `/pages/game/game?level=${nextLevel}`
    });
  },

  // 返回首页
  backToHome() {
    wx.navigateBack();
  },

  // 更新用户进度
  updateUserProgress() {
    const { currentLevel, score, currentStars } = this.data;
    
    // 更新全局数据
    const gameData = app.globalData.gameData;
    gameData.totalScore += score;
    gameData.currentLevel = Math.max(gameData.currentLevel, currentLevel);
    gameData.unlockedLevels = Math.max(gameData.unlockedLevels, currentLevel + 1);
    
    app.updateGameData(gameData);
  },

  // 显示提示
  showHint() {
    if (!this.data.canUseHint) {
      wx.showToast({
        title: '提示次数已用完',
        icon: 'none'
      });
      return;
    }
    
    const hints = [
      '寻找迷宫中的成语字符',
      '相邻的格子才能移动',
      '收集完整的成语可以获得更多分数',
      '到达终点完成关卡'
    ];
    
    const randomHint = hints[Math.floor(Math.random() * hints.length)];
    
    this.setData({
      showHintModal: true,
      currentHint: randomHint
    });
  },

  // 使用提示
  useHint() {
    const hintsUsed = this.data.hintsUsed + 1;
    const newScore = Math.max(0, this.data.score - 10);
    
    this.setData({
      hintsUsed,
      score: newScore,
      canUseHint: hintsUsed < 3
    });
    
    this.hideHintModal();
    
    wx.showToast({
      title: '提示已使用',
      icon: 'success'
    });
  },

  // 隐藏提示弹窗
  hideHintModal() {
    this.setData({
      showHintModal: false,
      currentHint: ''
    });
  },

  // 重置关卡
  resetLevel() {
    wx.showModal({
      title: '确认重置',
      content: '重置关卡将清除当前进度，确定要重置吗？',
      success: (res) => {
        if (res.confirm) {
          this.initNewGame();
        }
      }
    });
  },

  // 暂停游戏
  pauseGame() {
    this.setData({ isPaused: true });
    this.pauseGameTimer();
  },

  // 恢复游戏
  resumeGame() {
    this.setData({ isPaused: false });
    if (this.data.gameStarted && !this.data.gameCompleted) {
      this.startGameTimer();
    }
  },

  // 获取格子样式类
  getCellClass(cell) {
    let classes = ['maze-cell'];
    
    if (cell.type === 'wall') classes.push('wall');
    else if (cell.type === 'path') classes.push('path');
    else if (cell.type === 'visited') classes.push('visited');
    
    if (cell.hasPlayer) classes.push('has-player');
    if (cell.isTarget) classes.push('is-target');
    if (cell.character) classes.push('has-character');
    
    return classes.join(' ');
  },

  // 保存游戏进度
  saveGameProgress() {
    const gameProgress = {
      level: this.data.currentLevel,
      score: this.data.score,
      timeElapsed: this.data.timeElapsed,
      hintsUsed: this.data.hintsUsed,
      mazeData: this.data.mazeData,
      playerPosition: this.data.playerPosition,
      foundIdioms: this.data.foundIdioms
    };
    
    try {
      wx.setStorageSync('gameProgress', gameProgress);
    } catch (error) {
      console.error('保存游戏进度失败:', error);
    }
  },

  // 加载游戏进度
  loadGameProgress() {
    try {
      const gameProgress = wx.getStorageSync('gameProgress');
      if (gameProgress) {
        this.setData({
          score: gameProgress.score || 0,
          timeElapsed: gameProgress.timeElapsed || 0,
          hintsUsed: gameProgress.hintsUsed || 0,
          mazeData: gameProgress.mazeData || [],
          playerPosition: gameProgress.playerPosition || { row: 0, col: 0 },
          foundIdioms: gameProgress.foundIdioms || []
        });
        
        this.startGame();
      } else {
        this.initNewGame();
      }
    } catch (error) {
      console.error('加载游戏进度失败:', error);
      this.initNewGame();
    }
  },

  // 阻止弹窗关闭
  preventClose() {
    // 空函数，阻止事件冒泡
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `我正在挑战成语迷宫第${this.data.currentLevel}关！`,
      path: `/pages/game/game?level=${this.data.currentLevel}`,
      imageUrl: '/images/share-game.jpg'
    };
  }
});
