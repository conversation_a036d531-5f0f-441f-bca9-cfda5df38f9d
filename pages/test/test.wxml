<!-- pages/test/test.wxml -->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">API连接测试</text>
  </view>

  <view class="test-section">
    <text class="section-title">健康检查</text>
    <button class="test-btn" bindtap="testHealth">测试健康检查</button>
    <text class="result-text">{{healthResult}}</text>
  </view>

  <view class="test-section">
    <text class="section-title">成语接口</text>
    <button class="test-btn" bindtap="testIdioms">获取成语列表</button>
    <text class="result-text">{{idiomsResult}}</text>
  </view>

  <view class="test-section">
    <text class="section-title">关卡接口</text>
    <button class="test-btn" bindtap="testLevels">获取关卡列表</button>
    <text class="result-text">{{levelsResult}}</text>
  </view>

  <view class="test-section">
    <text class="section-title">迷宫生成</text>
    <button class="test-btn" bindtap="testMaze">生成迷宫</button>
    <text class="result-text">{{mazeResult}}</text>
  </view>

  <view class="test-section" wx:if="{{mazeData}}">
    <text class="section-title">迷宫预览</text>
    <view class="maze-preview">
      <view class="maze-grid" style="grid-template-columns: repeat({{mazeData.size}}, 1fr);">
        <view class="maze-cell {{getCellClass(cell)}}" 
              wx:for="{{mazeData.cells}}" wx:key="index"
              wx:for-item="row" wx:for-index="rowIndex">
          <view class="cell-item {{cell.type}}" 
                wx:for="{{row}}" wx:key="colIndex"
                wx:for-item="cell">
            <text wx:if="{{cell.character}}" class="cell-char">{{cell.character}}</text>
            <text wx:if="{{cell.is_start}}" class="cell-marker">🚶</text>
            <text wx:if="{{cell.is_end}}" class="cell-marker">🎯</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
