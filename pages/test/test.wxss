/* pages/test/test.wxss */

.test-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-header {
  text-align: center;
  margin-bottom: 20px;
}

.test-title {
  font-size: 24px;
  font-weight: bold;
  color: #1E3A8A;
}

.test-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #2D3748;
  margin-bottom: 12px;
}

.test-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}

.test-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.result-text {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #FFD700;
}

/* 迷宫预览样式 */
.maze-preview {
  margin-top: 12px;
}

.maze-grid {
  display: grid;
  gap: 1px;
  background-color: #2D3748;
  border-radius: 8px;
  padding: 2px;
  max-width: 300px;
  margin: 0 auto;
}

.cell-item {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border-radius: 1px;
  position: relative;
}

.cell-item.wall {
  background-color: #2D3748;
}

.cell-item.path {
  background-color: #f8f9fa;
}

.cell-char {
  color: #1E3A8A;
  font-size: 8px;
}

.cell-marker {
  font-size: 8px;
}
