/* pages/test/test.wxss */

.test-container {
  padding: var(--spacing-large);
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-header {
  text-align: center;
  margin-bottom: var(--spacing-large);
}

.test-title {
  font-size: 24px;
  font-weight: bold;
  color: var(--secondary-color);
}

.test-section {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-medium);
  margin-bottom: var(--spacing-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: var(--font-size-large);
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: var(--spacing-small);
}

.test-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, var(--primary-color), #FFA500);
  color: #333;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-medium);
  font-weight: 500;
  margin-bottom: var(--spacing-small);
}

.test-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.result-text {
  display: block;
  font-size: var(--font-size-small);
  color: #666;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: var(--spacing-small);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
}

/* 迷宫预览样式 */
.maze-preview {
  margin-top: var(--spacing-small);
}

.maze-grid {
  display: grid;
  gap: 1px;
  background-color: #2D3748;
  border-radius: var(--border-radius);
  padding: 2px;
  max-width: 300px;
  margin: 0 auto;
}

.cell-item {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border-radius: 1px;
  position: relative;
}

.cell-item.wall {
  background-color: #2D3748;
}

.cell-item.path {
  background-color: #f8f9fa;
}

.cell-char {
  color: var(--secondary-color);
  font-size: 8px;
}

.cell-marker {
  font-size: 8px;
}
