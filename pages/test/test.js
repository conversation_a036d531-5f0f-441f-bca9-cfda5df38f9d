// pages/test/test.js
const { gameApi, idiomApi } = require('../../utils/api.js');

Page({
  data: {
    healthResult: '',
    idiomsResult: '',
    levelsResult: '',
    mazeResult: '',
    mazeData: null
  },

  onLoad() {
    console.log('测试页面加载');
  },

  // 测试健康检查
  async testHealth() {
    try {
      this.setData({ healthResult: '测试中...' });
      
      const result = await new Promise((resolve, reject) => {
        wx.request({
          url: 'http://localhost:8080/health',
          method: 'GET',
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}`));
            }
          },
          fail: reject
        });
      });

      this.setData({ 
        healthResult: `✅ 连接成功: ${result.service} v${result.version}` 
      });
    } catch (error) {
      this.setData({ 
        healthResult: `❌ 连接失败: ${error.message}` 
      });
    }
  },

  // 测试成语接口
  async testIdioms() {
    try {
      this.setData({ idiomsResult: '获取中...' });
      
      const result = await idiomApi.getIdioms(1, 5);
      
      this.setData({ 
        idiomsResult: `✅ 获取成功: 共${result.total}个成语，前5个：${result.idioms.map(item => item.idiom).join('、')}` 
      });
    } catch (error) {
      this.setData({ 
        idiomsResult: `❌ 获取失败: ${error.message}` 
      });
    }
  },

  // 测试关卡接口
  async testLevels() {
    try {
      this.setData({ levelsResult: '获取中...' });
      
      const result = await gameApi.getLevels();
      
      this.setData({ 
        levelsResult: `✅ 获取成功: 共${result.total}个关卡，包括：${result.levels.map(item => item.name).join('、')}` 
      });
    } catch (error) {
      this.setData({ 
        levelsResult: `❌ 获取失败: ${error.message}` 
      });
    }
  },

  // 测试迷宫生成
  async testMaze() {
    try {
      this.setData({ mazeResult: '生成中...' });
      
      const result = await gameApi.generateMaze(5, 1);
      
      this.setData({ 
        mazeResult: `✅ 生成成功: ${result.size}x${result.size}迷宫`,
        mazeData: result
      });
    } catch (error) {
      this.setData({ 
        mazeResult: `❌ 生成失败: ${error.message}`,
        mazeData: null
      });
    }
  },

  // 获取格子样式
  getCellClass(cell) {
    let classes = ['maze-cell'];
    if (cell.type === 'wall') classes.push('wall');
    else if (cell.type === 'path') classes.push('path');
    return classes.join(' ');
  }
});
