// pages/test/test.js
const api = require('../../utils/api.js');
const gameApi = api.gameApi;
const idiomApi = api.idiomApi;

Page({
  data: {
    healthResult: '',
    idiomsResult: '',
    levelsResult: '',
    mazeResult: '',
    mazeData: null
  },

  onLoad() {
    console.log('测试页面加载');
  },

  // 测试健康检查
  testHealth: function() {
    const self = this;
    self.setData({ healthResult: '测试中...' });

    wx.request({
      url: 'http://localhost:8080/health',
      method: 'GET',
      success: function(res) {
        if (res.statusCode === 200) {
          self.setData({
            healthResult: '✅ 连接成功: ' + res.data.service + ' v' + res.data.version
          });
        } else {
          self.setData({
            healthResult: '❌ 连接失败: HTTP ' + res.statusCode
          });
        }
      },
      fail: function(error) {
        self.setData({
          healthResult: '❌ 连接失败: ' + error.errMsg
        });
      }
    });
  },

  // 测试成语接口
  testIdioms: function() {
    const self = this;
    self.setData({ idiomsResult: '获取中...' });

    idiomApi.getIdioms(1, 5).then(function(result) {
      const idiomNames = result.idioms.map(function(item) {
        return item.idiom;
      }).join('、');

      self.setData({
        idiomsResult: '✅ 获取成功: 共' + result.total + '个成语，前5个：' + idiomNames
      });
    }).catch(function(error) {
      self.setData({
        idiomsResult: '❌ 获取失败: ' + error.message
      });
    });
  },

  // 测试关卡接口
  testLevels: function() {
    const self = this;
    self.setData({ levelsResult: '获取中...' });

    gameApi.getLevels().then(function(result) {
      const levelNames = result.levels.map(function(item) {
        return item.name;
      }).join('、');

      self.setData({
        levelsResult: '✅ 获取成功: 共' + result.total + '个关卡，包括：' + levelNames
      });
    }).catch(function(error) {
      self.setData({
        levelsResult: '❌ 获取失败: ' + error.message
      });
    });
  },

  // 测试迷宫生成
  testMaze: function() {
    const self = this;
    self.setData({ mazeResult: '生成中...' });

    gameApi.generateMaze(5, 1).then(function(result) {
      // 将二维数组转换为一维数组用于显示
      var mazeFlat = [];
      for (var i = 0; i < result.size; i++) {
        for (var j = 0; j < result.size; j++) {
          mazeFlat.push(result.cells[i][j]);
        }
      }

      self.setData({
        mazeResult: '✅ 生成成功: ' + result.size + 'x' + result.size + '迷宫',
        mazeData: result,
        mazeFlat: mazeFlat
      });
    }).catch(function(error) {
      self.setData({
        mazeResult: '❌ 生成失败: ' + error.message,
        mazeData: null,
        mazeFlat: []
      });
    });
  },

  // 获取格子样式
  getCellClass: function(cell) {
    var classes = ['maze-cell'];
    if (cell.type === 'wall') classes.push('wall');
    else if (cell.type === 'path') classes.push('path');
    return classes.join(' ');
  }
});
