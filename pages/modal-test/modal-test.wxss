/* pages/modal-test/modal-test.wxss */

.test-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
}

.test-title {
  font-size: 24px;
  font-weight: bold;
  color: #2D3748;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.test-btn {
  width: 200px;
  height: 44px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.test-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 导入首页的弹窗样式 */
/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.level-modal,
.tutorial-modal {
  background-color: white;
  border-radius: 8px;
  width: 85%;
  max-width: 400px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideUp 0.3s ease-out;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #2D3748;
}

.modal-close {
  font-size: 20px;
  color: #999;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.modal-close:hover {
  background-color: #f5f5f5;
  color: #666;
}

.modal-close:active {
  transform: scale(0.9);
}

/* 关卡网格 */
.level-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  max-height: 50vh;
  overflow-y: auto;
}

.level-item {
  aspect-ratio: 1;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.level-item.unlocked {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  border-color: #FFD700;
}

.level-item.unlocked:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.level-item.locked {
  background-color: #f5f5f5;
  color: #999;
  border-color: #e0e0e0;
}

.level-item:active {
  transform: scale(0.95);
}

.level-number {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}

.level-stars {
  font-size: 12px;
  margin-top: 2px;
  display: flex;
  gap: 1px;
}

.level-lock {
  font-size: 18px;
  opacity: 0.6;
}

/* 教程内容 */
.tutorial-content {
  padding: 16px;
  max-height: 50vh;
  overflow-y: auto;
  flex: 1;
}

.tutorial-step {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #FFD700;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.step-title {
  display: block;
  font-weight: bold;
  color: #1E3A8A;
  margin-bottom: 8px;
  font-size: 16px;
}

.step-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 动画定义 */
@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 滚动条样式优化 */
.level-grid::-webkit-scrollbar,
.tutorial-content::-webkit-scrollbar {
  width: 4px;
}

.level-grid::-webkit-scrollbar-track,
.tutorial-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.level-grid::-webkit-scrollbar-thumb,
.tutorial-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.level-grid::-webkit-scrollbar-thumb:hover,
.tutorial-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
