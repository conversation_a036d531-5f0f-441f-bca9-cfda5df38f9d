// pages/modal-test/modal-test.js
Page({
  data: {
    showLevelModal: false,
    showTutorialModal: false,
    levels: Array.from({length: 10}, (_, i) => ({
      id: i + 1,
      name: `关卡${i + 1}`,
      stars: i < 3 ? 3 : 0,
      unlocked: i < 3
    }))
  },

  onLoad: function() {
    console.log('弹窗测试页面加载');
  },

  // 显示关卡选择弹窗
  showLevelModal: function() {
    this.setData({ showLevelModal: true });
  },

  // 隐藏关卡选择弹窗
  hideLevelModal: function() {
    this.setData({ showLevelModal: false });
  },

  // 显示教程弹窗
  showTutorialModal: function() {
    this.setData({ showTutorialModal: true });
  },

  // 隐藏教程弹窗
  hideTutorialModal: function() {
    this.setData({ showTutorialModal: false });
  },

  // 选择关卡
  selectLevel: function(e) {
    const level = e.currentTarget.dataset.level;
    wx.showToast({
      title: `选择了关卡${level}`,
      icon: 'success'
    });
    this.hideLevelModal();
  },

  // 阻止弹窗关闭
  preventClose: function() {
    // 空函数，阻止事件冒泡
  }
});
