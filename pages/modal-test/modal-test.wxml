<!-- pages/modal-test/modal-test.wxml -->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">弹窗样式测试</text>
  </view>

  <view class="test-buttons">
    <button class="test-btn" bindtap="showLevelModal">测试关卡选择弹窗</button>
    <button class="test-btn" bindtap="showTutorialModal">测试教程弹窗</button>
  </view>

  <!-- 关卡选择弹窗 -->
  <view class="modal-overlay" wx:if="{{showLevelModal}}" bindtap="hideLevelModal">
    <view class="level-modal" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">选择关卡</text>
        <text class="modal-close" bindtap="hideLevelModal">✕</text>
      </view>
      <scroll-view class="level-grid" scroll-y>
        <view class="level-item {{index < 3 ? 'unlocked' : 'locked'}}"
              wx:for="{{levels}}" wx:key="index"
              bindtap="selectLevel" data-level="{{index + 1}}">
          <text class="level-number">{{index + 1}}</text>
          <text class="level-stars" wx:if="{{index < 2}}">
            <text wx:for="{{3}}" wx:key="star">⭐</text>
          </text>
          <text class="level-lock" wx:if="{{index >= 3}}">🔒</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 教程弹窗 -->
  <view class="modal-overlay" wx:if="{{showTutorialModal}}" bindtap="hideTutorialModal">
    <view class="tutorial-modal" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">游戏教程</text>
        <text class="modal-close" bindtap="hideTutorialModal">✕</text>
      </view>
      <scroll-view class="tutorial-content" scroll-y>
        <view class="tutorial-step">
          <text class="step-title">🎯 游戏目标</text>
          <text class="step-desc">在迷宫中找到正确的成语路径，完成成语填空挑战。通过收集成语字符，学习中华传统文化。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">🕹️ 操作方法</text>
          <text class="step-desc">点击迷宫格子移动角色，收集成语字符。当收集到完整成语时，会弹出选择题让你选择正确的释义。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">⭐ 评分系统</text>
          <text class="step-desc">根据完成时间和使用提示次数获得1-3颗星。星级越高，解锁更多关卡。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">💡 提示功能</text>
          <text class="step-desc">每个关卡有3次提示机会，使用提示会减少得分，但能帮助你找到正确路径。</text>
        </view>
        <view class="tutorial-step">
          <text class="step-title">🏆 成就系统</text>
          <text class="step-desc">完成特定条件可获得成就奖励，如连续通关、完美通关等。</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
