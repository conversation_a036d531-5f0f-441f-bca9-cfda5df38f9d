{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T06:19:18.284Z", "updatedAt": "2025-07-29T06:19:18.286Z", "resourceCount": 3}, "resources": [{"id": "go-backend-developer", "source": "project", "protocol": "role", "name": "Go Backend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/go-backend-developer/go-backend-developer.role.md", "metadata": {"createdAt": "2025-07-29T06:19:18.285Z", "updatedAt": "2025-07-29T06:19:18.285Z", "scannedAt": "2025-07-29T06:19:18.285Z", "path": "role/go-backend-developer/go-backend-developer.role.md"}}, {"id": "miniprogram-developer", "source": "project", "protocol": "role", "name": "Miniprogram Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/miniprogram-developer/miniprogram-developer.role.md", "metadata": {"createdAt": "2025-07-29T06:19:18.285Z", "updatedAt": "2025-07-29T06:19:18.285Z", "scannedAt": "2025-07-29T06:19:18.285Z", "path": "role/miniprogram-developer/miniprogram-developer.role.md"}}, {"id": "ui-designer", "source": "project", "protocol": "role", "name": "Ui Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-designer/ui-designer.role.md", "metadata": {"createdAt": "2025-07-29T06:19:18.286Z", "updatedAt": "2025-07-29T06:19:18.286Z", "scannedAt": "2025-07-29T06:19:18.286Z", "path": "role/ui-designer/ui-designer.role.md"}}], "stats": {"totalResources": 3, "byProtocol": {"role": 3}, "bySource": {"project": 3}}}